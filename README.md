# JavaScript抓取脚本

这个Python脚本可以抓取指定URL页面中的所有JavaScript文件，包括外部引用的JS文件和内联的JavaScript代码。

## 功能特性

- 🔍 自动识别页面中的所有`<script>`标签
- 📥 下载外部JavaScript文件
- 📝 保存内联JavaScript代码
- 🌐 支持相对URL和绝对URL
- 🔄 自动处理重复文件名
- 🛡️ 模拟浏览器请求头，避免被反爬
- ⏱️ 可配置请求超时时间
- 📁 自动创建输出目录

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests beautifulsoup4 lxml
```

## 使用方法

### 基本用法

```bash
python js_scraper.py https://example.com
```

### 指定输出目录

```bash
python js_scraper.py https://example.com -o my_js_files
```

### 设置超时时间

```bash
python js_scraper.py https://example.com -t 30
```

### 完整参数

```bash
python js_scraper.py https://example.com --output js_output --timeout 20
```

## 参数说明

- `url`: 目标网页URL（必需）
- `-o, --output`: 输出目录（默认：js_files）
- `-t, --timeout`: 请求超时时间，单位秒（默认：10）

## 输出文件

脚本会在指定的输出目录中创建以下文件：

1. **外部JS文件**: 使用原始文件名或根据URL生成的文件名
2. **内联脚本**: 命名为`inline_script_1.js`, `inline_script_2.js`等

## 示例

假设要抓取百度首页的JavaScript文件：

```bash
python js_scraper.py https://www.baidu.com
```

输出示例：
```
开始抓取JavaScript文件...
目标URL: https://www.baidu.com
输出目录: E:\project\xianyujiedan\windsurf\chanpin\nixiang\xhs\js_files
--------------------------------------------------
正在获取页面: https://www.baidu.com
发现外部JS文件: https://ss1.bdstatic.com/5eN1bjq8AAUYm2zgoY3K/r/www/cache/bdorz/baidu.js
发现内联JS代码块 #1
发现内联JS代码块 #2

找到 1 个外部JS文件，2 个内联脚本
--------------------------------------------------
正在下载: https://ss1.bdstatic.com/5eN1bjq8AAUYm2zgoY3K/r/www/cache/bdorz/baidu.js
已保存: js_files\baidu.js
已保存内联脚本: js_files\inline_script_1.js
已保存内联脚本: js_files\inline_script_2.js
--------------------------------------------------
抓取完成！成功保存 3 个文件
文件保存在: E:\project\xianyujiedan\windsurf\chanpin\nixiang\xhs\js_files
```

## 注意事项

1. 某些网站可能有反爬虫机制，脚本已经设置了浏览器请求头来减少被检测的可能性
2. 请遵守网站的robots.txt和使用条款
3. 建议在请求之间添加适当的延迟，避免对目标服务器造成过大压力
4. 某些动态加载的JavaScript可能无法通过此方法获取，需要使用Selenium等工具

## 错误处理

脚本包含完善的错误处理机制：
- 网络请求失败会显示错误信息并继续处理其他文件
- 文件保存失败会显示具体错误原因
- 无效的URL会自动添加https://前缀

## 扩展功能

如果需要更高级的功能，可以考虑：
- 使用Selenium处理动态加载的内容
- 添加代理支持
- 支持批量URL处理
- 添加JavaScript代码美化功能
