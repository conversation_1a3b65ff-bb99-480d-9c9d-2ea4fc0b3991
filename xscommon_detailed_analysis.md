# 小红书 xsCommon 函数详细分析报告

## 🎯 核心发现

通过深度分析，我们成功提取了小红书 `x-s-common` 请求头的完整生成逻辑！

## 📋 函数签名

```javascript
function xsCommon(e, r)
```

**参数说明：**
- `e`: 包含 `platform` 属性的对象
- `r`: 请求对象，包含 `url` 和 `headers` 属性

## 🔍 算法流程分析

### 1. 前置检查
```javascript
// 检查URL是否在白名单中，如果不需要签名则直接返回
if (!utils_shouldSign(r.url)) return r;
```

### 2. 数据收集阶段
```javascript
var s = e.platform;                           // 平台信息
var u = r.url;                               // 请求URL
var c = r.headers["X-Sign"] || "";           // 现有的X-Sign头
var d = getSigCount(c);                      // 签名计数
var p = localStorage.getItem("b1");          // 本地存储的b1值
var f = localStorage.getItem("b1b1") || "1"; // 本地存储的b1b1值，默认为"1"
```

### 3. 核心对象构建
```javascript
var v = {
    s0: getPlatformCode(s),                  // 平台代码
    s1: "",                                  // 空字符串
    x0: f,                                   // b1b1值
    x1: C,                                   // 常量C
    x2: s || "PC",                          // 平台信息，默认"PC"
    x3: "xhs-pc-web",                       // 固定标识
    x4: "4.68.0",                           // 版本号
    x5: l.Z.get("a1"),                      // 从某个对象获取a1值
    x6: "",                                  // 空字符串
    x7: "",                                  // 空字符串
    x8: p,                                   // b1值
    x9: O("".concat("").concat("").concat(p)), // 对b1值进行某种处理
    x10: d,                                  // 签名计数
    x11: "normal"                           // 固定值"normal"
};
```

### 4. 指纹检测逻辑
```javascript
var h = k.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)});

if ((window.xhsFingerprintV3?.getCurMiniUa) && h) {
    // 异步获取指纹信息
    window.xhsFingerprintV3.getCurMiniUa(function(e){
        v.x8 = e;
        v.x9 = O("".concat("").concat("").concat(e));
        r.headers["X-S-Common"] = b64Encode(encodeUtf8(JSON.stringify(v)));
    });
} else {
    // 直接设置请求头
    r.headers["X-S-Common"] = b64Encode(encodeUtf8(JSON.stringify(v)));
}
```

## 🔧 依赖函数分析

### 1. getPlatformCode(platform)
```javascript
function getPlatformCode(e) {
    switch(e) {
        case "Android": return s.Android;
        case "iOS": return s.iOS;
        case "Mac OS": return s.MacOs;
        case "Linux": return s.Linux;
        default: return s.other;
    }
}
```

### 2. getSigCount(xSign)
```javascript
function getSigCount(e) {
    var r = Number(sessionStorage.getItem("sc")) || 0;
    return e && (r++, sessionStorage.setItem("sc", r.toString())), r;
}
```

### 3. utils_shouldSign(url)
```javascript
function utils_shouldSign(e) {
    var r = true;
    return e.indexOf(window.location.host) > -1 || 
           e.indexOf("sit.xiaohongshu.com") > -1 ? r : 
           (g.some(function(i) {
               if (e.indexOf(i) > -1) return r = false, true;
           }), r);
}
```

### 4. b64Encode(data)
```javascript
function b64Encode(e) {
    // 标准Base64编码实现
    for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)
        s.push(encodeChunk(e,u,u+16383>c?c:u+16383));
    return 1===a?(r=e[i-1],s.push(P[r>>2]+P[r<<4&63]+"==")):
           2===a&&(r=(e[i-2]<<8)+e[i-1],s.push(P[r>>10]+P[r>>4&63]+P[r<<2&63]+"=")),
           s.join("");
}
```

### 5. encodeUtf8(str)
```javascript
function encodeUtf8(e) {
    for(var r=encodeURIComponent(e),i=[],a=0;a<r.length;a++) {
        var s=r.charAt(a);
        if("%"===s) {
            var u=parseInt(r.charAt(a+1)+r.charAt(a+2),16);
            i.push(u),a+=2;
        } else i.push(s.charCodeAt(0));
    }
    return i;
}
```

### 6. O函数 (CRC32)
```javascript
// 这是一个CRC32哈希函数
var O = function(e) {
    for(var r,i,a=256,s=[];a--;s[a]=r>>>0)
        for(i=8,r=a;i--;)r=1&r?r>>>1^0xedb88320:r>>>1;
    return function(e) {
        if("string"==typeof e) {
            for(var r=0,i=-1;r<e.length;++r)
                i=s[255&i^e.charCodeAt(r)]^i>>>8;
            return -1^i^0xedb88320;
        }
        for(var r=0,i=-1;r<e.length;++r)
            i=s[255&i^e[r]]^i>>>8;
        return -1^i^0xedb88320;
    };
}();
```

## 📊 关键常量

从分析中发现的重要常量：
- `C`: 某个重要的常量值
- `s.Android`, `s.iOS`, `s.MacOs`, `s.Linux`, `s.other`: 平台代码映射
- `l.Z`: 包含get方法的对象，用于获取a1值
- `k`: 用于URL匹配的正则表达式数组
- `g`: 用于判断是否需要签名的URL模式数组

## 🎯 x-s-common 生成流程总结

1. **输入验证**: 检查URL是否需要签名
2. **数据收集**: 从localStorage、sessionStorage等收集必要信息
3. **对象构建**: 构建包含14个字段的对象
4. **指纹处理**: 可能异步获取设备指纹信息
5. **编码输出**: JSON序列化 → UTF8编码 → Base64编码

## 🔑 关键发现

1. **x-s-common的本质**: 是一个包含平台信息、版本号、用户标识、设备指纹等信息的JSON对象，经过UTF8编码和Base64编码后的字符串

2. **核心字段**:
   - `s0`: 平台代码
   - `x0`: localStorage中的"b1b1"值
   - `x1`: 常量C
   - `x2`: 平台信息
   - `x3`: "xhs-pc-web"
   - `x4`: "4.68.0"
   - `x5`: 从某个对象获取的"a1"值
   - `x8`: localStorage中的"b1"值
   - `x9`: 对x8进行CRC32哈希的结果
   - `x10`: 签名计数
   - `x11`: "normal"

3. **动态部分**: x8和x9可能会被设备指纹信息替换

4. **编码方式**: JSON.stringify → encodeUtf8 → b64Encode

这个分析为我们提供了完整的x-s-common生成逻辑，可以用于理解小红书的请求签名机制。
