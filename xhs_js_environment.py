#!/usr/bin/env python3
"""
小红书 JavaScript 环境补全方案
使用 execjs 或 PyV8 执行原始 JavaScript 代码
"""

import json
import os
from pathlib import Path

try:
    import execjs
    HAS_EXECJS = True
except ImportError:
    HAS_EXECJS = False
    print("警告: 未安装 execjs，请运行: pip install PyExecJS")


class XHSJSEnvironment:
    """小红书JavaScript环境模拟器"""
    
    def __init__(self, js_file_path="xhsJsFiles/vendor-dynamic.f0f5c43a.js"):
        """
        初始化JS环境
        
        Args:
            js_file_path: JavaScript文件路径
        """
        self.js_file_path = Path(js_file_path)
        self.ctx = None
        self.js_code = ""
        
        if not HAS_EXECJS:
            raise ImportError("需要安装 PyExecJS: pip install PyExecJS")
        
        self.load_js_code()
        self.setup_environment()
    
    def load_js_code(self):
        """加载JavaScript代码"""
        try:
            with open(self.js_file_path, 'r', encoding='utf-8') as f:
                self.js_code = f.read()
            print(f"成功加载JS文件: {self.js_file_path}")
        except Exception as e:
            print(f"加载JS文件失败: {e}")
            raise
    
    def setup_environment(self):
        """设置JavaScript执行环境"""
        
        # 补全浏览器环境
        browser_env = """
        // 模拟浏览器环境
        var window = this;
        var document = {
            createElement: function() { return {}; },
            getElementById: function() { return null; },
            addEventListener: function() {}
        };
        
        var navigator = {
            userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            platform: "Win32",
            hardwareConcurrency: 8,
            deviceMemory: 8
        };
        
        var location = {
            href: "https://www.xiaohongshu.com/",
            host: "www.xiaohongshu.com",
            pathname: "/",
            search: "",
            hash: ""
        };
        
        var localStorage = {
            data: {},
            getItem: function(key) {
                return this.data[key] || null;
            },
            setItem: function(key, value) {
                this.data[key] = String(value);
            },
            removeItem: function(key) {
                delete this.data[key];
            }
        };
        
        var sessionStorage = {
            data: {},
            getItem: function(key) {
                return this.data[key] || null;
            },
            setItem: function(key, value) {
                this.data[key] = String(value);
            },
            removeItem: function(key) {
                delete this.data[key];
            }
        };
        
        // 模拟一些可能需要的全局变量
        var g = []; // URL模式数组
        var k = []; // 正则表达式数组
        var S = []; // 另一个数组
        var C = "default_constant_c"; // 常量C
        var l = {
            Z: {
                get: function(key) {
                    if (key === "a1") return "default_a1_value";
                    return null;
                }
            }
        };
        
        // 设置一些localStorage默认值
        localStorage.setItem("b1", "default_b1_value");
        localStorage.setItem("b1b1", "1");
        
        // 模拟指纹对象
        window.xhsFingerprintV3 = {
            getCurMiniUa: function(callback) {
                // 模拟异步获取指纹
                setTimeout(function() {
                    callback("fingerprint_mock_value");
                }, 10);
            }
        };
        """
        
        try:
            # 创建JavaScript执行上下文
            full_code = browser_env + "\n" + self.js_code
            self.ctx = execjs.compile(full_code)
            print("JavaScript环境设置完成")
        except Exception as e:
            print(f"设置JavaScript环境失败: {e}")
            raise
    
    def call_xscommon(self, platform="PC", url="https://edith.xiaohongshu.com/api/sns/web/v1/feed"):
        """
        调用xsCommon函数
        
        Args:
            platform: 平台信息
            url: 请求URL
            
        Returns:
            生成的x-s-common值
        """
        if not self.ctx:
            raise RuntimeError("JavaScript环境未初始化")
        
        try:
            # 准备参数
            e_param = {"platform": platform}
            r_param = {
                "url": url,
                "headers": {}
            }
            
            # 调用xsCommon函数
            result = self.ctx.call("xsCommon", e_param, r_param)
            
            # 提取x-s-common
            x_s_common = result.get("headers", {}).get("X-S-Common", "")
            
            return x_s_common
            
        except Exception as e:
            print(f"调用xsCommon失败: {e}")
            return None
    
    def set_storage_values(self, b1=None, b1b1=None, a1=None):
        """
        设置存储值
        
        Args:
            b1: localStorage中的b1值
            b1b1: localStorage中的b1b1值  
            a1: a1值
        """
        if not self.ctx:
            return
        
        try:
            if b1:
                self.ctx.eval(f'localStorage.setItem("b1", "{b1}");')
            if b1b1:
                self.ctx.eval(f'localStorage.setItem("b1b1", "{b1b1}");')
            if a1:
                self.ctx.eval(f'l.Z.get = function(key) {{ if (key === "a1") return "{a1}"; return null; }};')
            
            print("存储值设置完成")
        except Exception as e:
            print(f"设置存储值失败: {e}")
    
    def test_functions(self):
        """测试各个依赖函数"""
        if not self.ctx:
            return
        
        test_results = {}
        
        # 测试函数列表
        test_functions = [
            "getPlatformCode",
            "getSigCount", 
            "utils_shouldSign",
            "b64Encode",
            "encodeUtf8"
        ]
        
        for func_name in test_functions:
            try:
                # 根据函数名准备测试参数
                if func_name == "getPlatformCode":
                    result = self.ctx.call(func_name, "PC")
                elif func_name == "getSigCount":
                    result = self.ctx.call(func_name, "")
                elif func_name == "utils_shouldSign":
                    result = self.ctx.call(func_name, "https://www.xiaohongshu.com/")
                elif func_name == "b64Encode":
                    result = self.ctx.call(func_name, [72, 101, 108, 108, 111])  # "Hello"
                elif func_name == "encodeUtf8":
                    result = self.ctx.call(func_name, "Hello")
                else:
                    result = "未测试"
                
                test_results[func_name] = result
                print(f"✅ {func_name}: {result}")
                
            except Exception as e:
                test_results[func_name] = f"错误: {e}"
                print(f"❌ {func_name}: {e}")
        
        return test_results


def main():
    """主函数示例"""
    if not HAS_EXECJS:
        print("请先安装依赖: pip install PyExecJS")
        return
    
    try:
        # 创建JS环境
        print("=== 小红书 JavaScript 环境补全 ===")
        env = XHSJSEnvironment()
        
        # 设置自定义参数
        env.set_storage_values(
            b1="custom_b1_value",
            b1b1="1", 
            a1="custom_a1_value"
        )
        
        # 测试依赖函数
        print("\n=== 测试依赖函数 ===")
        env.test_functions()
        
        # 生成x-s-common
        print("\n=== 生成 x-s-common ===")
        x_s_common = env.call_xscommon(
            platform="PC",
            url="https://edith.xiaohongshu.com/api/sns/web/v1/feed"
        )
        
        if x_s_common:
            print(f"生成的 x-s-common: {x_s_common}")
            
            # 尝试解码验证
            try:
                import base64
                decoded_bytes = base64.b64decode(x_s_common)
                decoded_str = decoded_bytes.decode('utf-8')
                decoded_obj = json.loads(decoded_str)
                
                print("\n解码后的对象:")
                for key, value in decoded_obj.items():
                    print(f"  {key}: {value}")
                    
            except Exception as e:
                print(f"解码失败: {e}")
        else:
            print("生成x-s-common失败")
            
    except Exception as e:
        print(f"环境初始化失败: {e}")
        print("\n可能的解决方案:")
        print("1. 安装 PyExecJS: pip install PyExecJS")
        print("2. 安装 Node.js (PyExecJS的依赖)")
        print("3. 检查JavaScript文件路径是否正确")


if __name__ == "__main__":
    main()
