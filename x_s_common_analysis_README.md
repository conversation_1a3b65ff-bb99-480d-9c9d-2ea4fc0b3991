# x-s-common 分析工具

这个工具包含两个脚本，用于分析JavaScript文件中与`x-s-common`请求头相关的代码。

## 📁 文件说明

### 1. `x_s_common_extractor.py` - 通用提取器
通用的JavaScript分析工具，可以分析任何网站的JS文件。

### 2. `xhs_x_s_common_analyzer.py` - 小红书专用分析器
专门针对小红书(XHS)的JavaScript文件进行深度分析，包含更多小红书特有的模式识别。

## 🚀 使用方法

### 通用提取器

```bash
# 分析当前目录的所有JS文件
python x_s_common_extractor.py

# 指定JS文件目录
python x_s_common_extractor.py -d ./js_files

# 指定输出目录
python x_s_common_extractor.py -d ./js_files -o ./analysis_output
```

### 小红书专用分析器

```bash
# 分析xhsJsFiles目录（默认）
python xhs_x_s_common_analyzer.py

# 指定目录
python xhs_x_s_common_analyzer.py -d ./my_xhs_js_files

# 指定输出目录
python xhs_x_s_common_analyzer.py -d ./xhsJsFiles -o ./xhs_analysis_results
```

## 🔍 分析内容

### 通用提取器分析的模式：
- **x-s-common直接引用**: `"x-s-common"`, `X-S-Common`等
- **请求头设置**: `setRequestHeader`, `headers["x-s-common"]`等
- **函数名模式**: 包含`sign`, `encrypt`, `header`等关键词的函数
- **加密相关**: `md5`, `sha`, `hmac`, `CryptoJS`等
- **时间戳相关**: `Date.now()`, `getTime()`等

### 小红书专用分析器额外分析：
- **签名函数**: 更精确的签名函数识别
- **加密函数**: 小红书特有的加密模式
- **URL路径操作**: `pathname`, `location.pathname`等
- **请求数据操作**: `JSON.stringify`, `data`, `payload`等
- **加密链路分析**: 分析完整的加密流程
- **生成器识别**: 自动识别可能的x-s-common生成函数

## 📊 输出结果

### 通用提取器输出：
- `analysis_results.json` - 完整的分析结果
- `extracted_functions.js` - 提取的相关函数
- `analysis_summary.txt` - 分析摘要

### 小红书专用分析器输出：
- `xhs_analysis_results.json` - 完整的分析结果
- `potential_x_s_common_generators.js` - 潜在的x-s-common生成器（按置信度排序）
- `all_extracted_functions.js` - 所有提取的相关函数
- `analysis_report.md` - 详细的分析报告

## 🎯 重点关注

### 在小红书分析结果中重点查看：

1. **`potential_x_s_common_generators.js`** - 这个文件包含了最有可能生成x-s-common的函数，按置信度排序

2. **分析报告中的"置信度最高的生成器"** - 这些函数最有可能是你要找的

3. **包含多种模式的函数** - 同时包含加密、时间戳、URL路径等操作的函数

## 💡 使用建议

1. **先运行小红书专用分析器**：
   ```bash
   python xhs_x_s_common_analyzer.py
   ```

2. **查看置信度最高的生成器**：
   打开 `xhs_x_s_common_analysis/potential_x_s_common_generators.js`

3. **分析函数逻辑**：
   重点关注包含以下操作的函数：
   - 时间戳获取 (`Date.now()`)
   - URL路径获取 (`pathname`)
   - 数据序列化 (`JSON.stringify`)
   - 加密操作 (`md5`, `sha`, `hmac`)

4. **查看完整报告**：
   打开 `xhs_x_s_common_analysis/analysis_report.md` 了解详细统计

## 🔧 高级用法

### 分析特定文件
如果你知道某个特定的JS文件可能包含x-s-common生成逻辑，可以：

1. 将该文件单独放在一个目录中
2. 运行分析器：
   ```bash
   python xhs_x_s_common_analyzer.py -d ./single_file_dir
   ```

### 批量分析
如果你有多个不同来源的JS文件：

1. 按来源分别放在不同目录
2. 分别运行分析器
3. 对比分析结果

## 📝 示例输出

运行小红书分析器后，你会看到类似这样的输出：

```
小红书 x-s-common 分析器
============================================================
在 xhsJsFiles 中找到 12 个JavaScript文件
------------------------------------------------------------
正在分析: index.788b3226.js
  发现 15 个匹配项
  提取 3 个相关函数
  识别 1 个潜在生成器
正在分析: vendor.621a7319.js
  发现 8 个匹配项
  提取 2 个相关函数
  识别 0 个潜在生成器
...
------------------------------------------------------------
分析完成！
  分析文件数: 12
  发现匹配的文件数: 8
  提取的函数数: 25
  潜在生成器数: 3

分析结果已保存到: E:\project\...\xhs_x_s_common_analysis
  - 完整结果: xhs_analysis_results.json
  - 潜在生成器: potential_x_s_common_generators.js
  - 所有函数: all_extracted_functions.js
  - 分析报告: analysis_report.md
```

重点查看 `potential_x_s_common_generators.js` 文件中置信度最高的函数！
