# xsCommon函数深度分析报告

## xsCommon函数实现

```javascript
{var i,a;try{var s=e.platform,u=r.url;if(S.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)}),!utils_shouldSign(u))return r;var c=r.headers["X-Sign"]||"",d=getSigCount(c),p=localStorage.getItem("b1"),f=localStorage.getItem("b1b1")||"1",v={s0:getPlatformCode(s),s1:"",x0:f,x1:C,x2:s||"PC",x3:"xhs-pc-web",x4:"4.68.0",x5:l.Z.get("a1"),x6:"",x7:"",x8:p,x9:O("".concat("").concat("").concat(p)),x10:d,x11:"normal"},h=k.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)});(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}
```

## 算法分析

### 函数参数

### 局部变量
- `s` = `e.platform`
- `c` = `r.headers["X-Sign"]||""`

### 对象结构（v对象）
- `s0`: `getPlatformCode(s)`
- `s1`: `""`
- `x0`: `f`
- `x1`: `C`
- `x2`: `s||"PC"`
- `x3`: `"xhs-pc-web"`
- `x4`: `"4.68.0"`
- `x5`: `l.Z.get("a1")`
- `x6`: `""`
- `x7`: `""`
- `x8`: `p`
- `x9`: `O("".concat("").concat("").concat(p))`
- `x10`: `d`
- `x11`: `"normal"`

### 关键操作
- **storage_access**: `localStorage.getItem("b1")`
- **storage_access**: `localStorage.getItem("b1b1")`
- **serialization**: `JSON.stringify(v)`
- **serialization**: `JSON.stringify(v)`
- **serialization**: `b64Encode(encodeUtf8(JSON.stringify(v)`
- **serialization**: `b64Encode(encodeUtf8(JSON.stringify(v)`
- **serialization**: `encodeUtf8(JSON.stringify(v)`
- **serialization**: `encodeUtf8(JSON.stringify(v)`
- **serialization**: `headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}`
- **data_access**: `.get("a1")`
- **hash_or_encrypt**: `O("".concat("")`
- **hash_or_encrypt**: `O("".concat("")`

## 依赖函数

### getPlatformCode
```javascript
{switch(e){case"Android":return s.Android;case"iOS":return s.iOS;case"Mac OS":return s.MacOs;case"Linux":return s.Linux;default:return s.other}}
```

### getSigCount
```javascript
{var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}
```

### utils_shouldSign
```javascript
{var r=!0;return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g.some(function(i){if(e.indexOf(i)>-1)return r=!1,!0}),r)}
```

### b64Encode
```javascript
{for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)s.push(encodeChunk(e,u,u+16383>c?c:u+16383));return 1===a?(r=e[i-1],s.push(P[r>>2]+P[r<<4&63]+"==")):2===a&&(r=(e[i-2]<<8)+e[i-1],s.push(P[r>>10]+P[r>>4&63]+P[r<<2&63]+"=")),s.join("")}
```

### encodeUtf8
```javascript
{for(var r=encodeURIComponent(e),i=[],a=0;a<r.length;a++){var s=r.charAt(a);if("%"===s){var u=parseInt(r.charAt(a+1)+r.charAt(a+2),16);i.push(u),a+=2}else i.push(s.charCodeAt(0))}return i}
```

### O
```javascript
{return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}
```

```javascript
{return postApiSnsWebV1LoginSocial}
```

```javascript
{var e;if(null===(e=window)||void 0===e?void 0:e.__baseInfo__)try{var r=JSON.parse(window.__baseInfo__);D.value=Object.keys(r).length?r:void 0}catch(e){D.value=void 0}else D.value=void 0}
```

```javascript
{var r=e.userId,i=e.userToken,a=e.sessionId,s=e.hashExp;return r&&(Z.userId=r),i&&(Z.userToken=i),a&&(Z.sessionId=a),s&&"string"==typeof s&&(Z.hashExp=s),Z}
```

```javascript
{if(!isBrowser())return{};var r=e?getPath(e):parseUrl(window.location.href);return $.setSessionId(r),{context_matchedPath:r,context_route:window.location.href,context_userAgent:window.navigator.userAgent}}
```

```javascript
{arguments.length>0&&void 0!==arguments[0]&&arguments[0];var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.getUserInfo;return r?new Promise(function(e){setTimeout(function(){r().then(function(r){e(r)}).catch(function(){e(ee)})})}):getUserInfoPromise()}
```

```javascript
{var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return et=getUserInfo(e,r)}
```

```javascript
{var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r={artifactName:e.package&&e.package.name||"xhs-pc-web",artifactVersion:e.package&&e.package.version||"4.68.0"};return e.getArtifactInfo?(0,g._)({},r,e.getArtifactInfo()):r}
```

```javascript
{var e,r;return{cpuCores:null===(e=window.navigator)||void 0===e?void 0:e.hardwareConcurrency,deviceMemory:null===(r=window.navigator)||void 0===r?void 0:r.deviceMemory}}
```

```javascript
{for(var r,i,a=256,s=[];a--;s[a]=r>>>0)for(i=8,r=a;i--;)r=1&r?r>>>1^0xedb88320:r>>>1;return function(e){if("string"==typeof e){for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e.charCodeAt(r)]^i>>>8;return -1^i^0xedb88320}for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e[r]]^i>>>8;return -1^i^0xedb88320}}
```

```javascript
{return Object.prototype.hasOwnProperty.call(e,r)}
```

```javascript
{var i,a,s,u,c,l,d,p,f,v,h,g,m,_,y,w,E,T="",S="",b=0,k=0,C=0,P=0,A=0,R=0;if((null===(a=e.event)||void 0===a?void 0:null===(i=a.value)||void 0===i?void 0:i.pointId)&&(b=e.event.value.pointId),null===(c=e.page)||void 0===c?void 0:null===(u=c.value)||void 0===u?void 0:null===(s=u.pageInstance)||void 0===s?void 0:s.value){T=e.page.value.pageInstance.value;var I=e.page.value.pageInstance.value.toUpperCase();k=(null==r?void 0:r.PageInstance["".concat(I)])||0}if(null===(p=e.event)||void 0===p?void 0:null===(d=p.value)||void 0===d?void 0:null===(l=d.action)||void 0===l?void 0:l.value){var O=e.event.value.action.value.toUpperCase();C=(null==r?void 0:r.NormalizedAction["".concat(O)])||0}if(null===(h=e.event)||void 0===h?void 0:null===(v=h.value)||void 0===v?void 0:null===(f=v.actionInteractionType)||void 0===f?void 0:f.value){var N=e.event.value.actionInteractionType.value.toUpperCase();P=(null==r?void 0:r.ActionInteractionType["".concat(N)])||0}if(null===(_=e.event)||void 0===_?void 0:null===(m=_.value)||void 0===m?void 0:null===(g=m.targetType)||void 0===g?void 0:g.value){var L=e.event.value.targetType.value.toUpperCase();A=(null==r?void 0:r.RichTargetType["".concat(L)])||0}if(null===(E=e.event)||void 0===E?void 0:null===(w=E.value)||void 0===w?void 0:null===(y=w.targetDisplayType)||void 0===y?void 0:y.value){var M=e.event.value.targetDisplayType.value.toUpperCase();R=(null==r?void 0:r.TargetDisplayType["".concat(M)])||0}return S="".concat(k,"^").concat(C,"^").concat(P,"^").concat(A,"^").concat(R),{pointId:b,pageInstanceStr:T,pageInstance:k,action:C,actionInteractionType:P,targetType:A,targetDisplayType:R,referKey:S}}
```

```javascript
{return getABInfo().then(function(e){return{user:{type:"User",value:{userId:e.userId||"",hashUserId:e.userToken,wxOpenid:getOpenId()||"",expV4:e.hashExp}}}})}
```

```javascript
{var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=i.getUserInfo?i.getUserInfo():r.purgeUser?purgeUserInfo():meta_user(),s={artifactName:"xhs-pc-web",artifactVersion:"4.68.0"},u=i.getArtifactInfo?(0,v._)({},s,i.getArtifactInfo()):s;return Promise.all([(e=i,new Promise(function(r){y.YF.isXHS?(0,eu.dw)("getDeviceInfo").then(function(e){e.value?r(deviceBuilderV2(e.value)):r(deviceBuilderV2())}).catch(function(){r(deviceBuilderV2())}):e.getDeviceInfo?e.getDeviceInfo().then(function(e){r(deviceBuilderV2({},e))}).catch(function(){r(deviceBuilderV2())}):r(deviceBuilderV2())})),eQ,a,getBrowserInfoV2(r.route),u]).then(function(e){var r=(0,j._)(e,5),i=r[0],a=r[1],s=r[2],u=r[3],c=r[4],l=(0,v._)({},i,a,u);return l.context_artifactName=c.artifactName,l.context_artifactVersion=c.artifactVersion,(null==s?void 0:s.user)&&(l.context_userId=s.user.value.userId),l})}
```

```javascript
{return data_meta(e,w).then(function(e){e5.extend(e,eN.NAME),e5.extend(e,eM.NAME),e5.extend(e,eL.NAME),r()})}
```

```javascript
{return getBaseInfo(e,w).then(function(e){e9[ex.NAME]=e,r()})}
```

```javascript
{setUserId(e9,e)}
```

```javascript
{return h}
```

```javascript
{return webp2png(e),e}
```

```javascript
{return prado_invoke_invoke}
```

```javascript
{return prado_invoke_subscribe}
```

```javascript
{return webp2png(e),e}
```

```javascript
{return validateRes}
```

```javascript
{var e,r,i,a,s,u,c=null!==(r=null===(e=navigator)||void 0===e?void 0:e.userAgent)&&void 0!==r?r:"";return c.indexOf("Opera")>-1||c.indexOf("OPR")>-1?(i="Opera",a=(a=c.match(/(Opera|OPR)\/?\s*(\.?\d+(\.\d+)*)/i))?a[2]:"0.0.0"):c.indexOf("Edg")>-1?(i="Microsoft Edge",a=(a=c.match(/(Edg)\/?\s*(\.?\d+(\.\d+)*)/i))?a[2]:"0.0.0"):c.indexOf("Chrome")>-1?(i="Chrome",a=(a=c.match(/(Chrome)\/?\s*(\.?\d+(\.\d+)*)/i))?a[2]:"0.0.0"):c.indexOf("Safari")>-1?(i="Safari",a=(a=c.match(/(Safari)\/?\s*(\.?\d+(\.\d+)*)/i))?a[2]:"0.0.0"):c.indexOf("Firefox")>-1?(i="Firefox",a=(a=c.match(/(Firefox)\/?\s*(\.?\d+(\.\d+)*)/i))?a[2]:"0.0.0"):(i="Unknown",a="0.0.0"),-1!==c.indexOf("Windows")?(s="Windows",u=(u=c.match(/Windows NT\s*(\d+\.\d+)/))?u[1]:"Unknown"):-1!==c.indexOf("Mac OS X")?(s="macOS",u=(u=c.match(/Mac OS X\s*(\d+[_.]\d+)/))?u[1].replace(/_/g,"."):"Unknown"):-1!==c.indexOf("Android")?(s="Android",u=(u=c.match(/Android\s*(\d+\.\d+)/))?u[1]:"Unknown"):(-1!==c.indexOf("Linux")?s="Linux":s="Unknown",u="Unknown"),{browserName:i,browserVersion:a,osName:s,osVersion:u,userAgent:c}}
```

```javascript
{for(var r,i,a=256,s=[];a--;s[a]=r>>>0)for(i=8,r=a;i--;)r=1&r?r>>>1^0xedb88320:r>>>1;return function(e){if("string"==typeof e){for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e.charCodeAt(r)]^i>>>8;return -1^i^0xedb88320}for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e[r]]^i>>>8;return -1^i^0xedb88320}}
```

