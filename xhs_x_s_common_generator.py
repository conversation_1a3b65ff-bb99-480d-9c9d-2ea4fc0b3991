#!/usr/bin/env python3
"""
小红书 x-s-common 生成器 (Python实现)
基于对JavaScript代码的深度分析实现
"""

import json
import base64
import urllib.parse
import zlib
from typing import Dict, Any, Optional


class XHSXSCommonGenerator:
    """小红书 x-s-common 请求头生成器"""
    
    # 平台代码映射 (需要根据实际JS代码中的s对象确定具体值)
    PLATFORM_CODES = {
        "Android": 1,
        "iOS": 2, 
        "Mac OS": 3,
        "MacOs": 3,
        "Linux": 4,
        "PC": 5,
        "other": 5
    }
    
    def __init__(self):
        """初始化生成器"""
        self.version = "4.68.0"
        self.app_name = "xhs-pc-web"
        self.sig_count = 0
        
    def get_platform_code(self, platform: str) -> int:
        """获取平台代码"""
        return self.PLATFORM_CODES.get(platform, self.PLATFORM_CODES["other"])
    
    def crc32_hash(self, data: str) -> int:
        """CRC32哈希函数 (模拟JavaScript中的O函数)"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        return zlib.crc32(data) & 0xffffffff
    
    def encode_utf8(self, text: str) -> list:
        """UTF8编码 (模拟JavaScript中的encodeUtf8函数)"""
        encoded = urllib.parse.quote(text, safe='')
        result = []
        i = 0
        while i < len(encoded):
            char = encoded[i]
            if char == '%':
                # 解析十六进制编码
                hex_code = encoded[i+1:i+3]
                result.append(int(hex_code, 16))
                i += 3
            else:
                result.append(ord(char))
                i += 1
        return result
    
    def b64_encode(self, data: list) -> str:
        """Base64编码 (模拟JavaScript中的b64Encode函数)"""
        # 将数字列表转换为字节
        byte_data = bytes(data)
        # 使用标准base64编码
        return base64.b64encode(byte_data).decode('ascii')
    
    def should_sign(self, url: str) -> bool:
        """判断URL是否需要签名"""
        # 简化实现，实际需要根据JS中的g数组和逻辑判断
        # 这里假设所有小红书相关URL都需要签名
        xiaohongshu_domains = [
            'xiaohongshu.com',
            'xhscdn.com',
            'sit.xiaohongshu.com'
        ]
        return any(domain in url for domain in xiaohongshu_domains)
    
    def get_sig_count(self, x_sign: str = "") -> int:
        """获取签名计数"""
        if x_sign:
            self.sig_count += 1
        return self.sig_count
    
    def generate_x_s_common(self, 
                           platform: str = "PC",
                           url: str = "",
                           b1: str = "",
                           b1b1: str = "1", 
                           a1: str = "",
                           x_sign: str = "",
                           constant_c: Any = None,
                           use_fingerprint: bool = False,
                           fingerprint_data: str = "") -> str:
        """
        生成 x-s-common 请求头
        
        Args:
            platform: 平台信息 ("PC", "Android", "iOS", "Mac OS", "Linux")
            url: 请求URL
            b1: localStorage中的b1值
            b1b1: localStorage中的b1b1值，默认"1"
            a1: 从某个对象获取的a1值
            x_sign: 现有的X-Sign头
            constant_c: 常量C的值
            use_fingerprint: 是否使用设备指纹
            fingerprint_data: 设备指纹数据
            
        Returns:
            生成的x-s-common字符串
        """
        
        # 检查是否需要签名
        if url and not self.should_sign(url):
            return ""
        
        # 获取签名计数
        sig_count = self.get_sig_count(x_sign)
        
        # 确定使用的b1值 (如果有指纹数据则使用指纹数据)
        effective_b1 = fingerprint_data if use_fingerprint and fingerprint_data else b1
        
        # 构建核心对象
        v_object = {
            "s0": self.get_platform_code(platform),
            "s1": "",
            "x0": b1b1,
            "x1": constant_c,  # 需要根据实际情况确定
            "x2": platform or "PC",
            "x3": self.app_name,
            "x4": self.version,
            "x5": a1,
            "x6": "",
            "x7": "",
            "x8": effective_b1,
            "x9": self.crc32_hash(effective_b1) if effective_b1 else "",
            "x10": sig_count,
            "x11": "normal"
        }
        
        # JSON序列化
        json_str = json.dumps(v_object, separators=(',', ':'), ensure_ascii=False)
        
        # UTF8编码
        utf8_data = self.encode_utf8(json_str)
        
        # Base64编码
        b64_result = self.b64_encode(utf8_data)
        
        return b64_result
    
    def decode_x_s_common(self, x_s_common: str) -> Dict[str, Any]:
        """
        解码 x-s-common 请求头 (用于调试和验证)
        
        Args:
            x_s_common: x-s-common字符串
            
        Returns:
            解码后的对象
        """
        try:
            # Base64解码
            decoded_bytes = base64.b64decode(x_s_common)
            
            # UTF8解码
            decoded_str = decoded_bytes.decode('utf-8')
            
            # JSON解析
            decoded_obj = json.loads(decoded_str)
            
            return decoded_obj
        except Exception as e:
            print(f"解码失败: {e}")
            return {}


def main():
    """示例用法"""
    generator = XHSXSCommonGenerator()
    
    # 示例参数
    params = {
        "platform": "PC",
        "url": "https://edith.xiaohongshu.com/api/sns/web/v1/feed",
        "b1": "sample_b1_value",
        "b1b1": "1",
        "a1": "sample_a1_value",
        "x_sign": "",
        "constant_c": "sample_constant_c"
    }
    
    # 生成 x-s-common
    x_s_common = generator.generate_x_s_common(**params)
    
    print("=== 小红书 x-s-common 生成器 ===")
    print(f"生成的 x-s-common: {x_s_common}")
    print()
    
    # 解码验证
    decoded = generator.decode_x_s_common(x_s_common)
    print("解码后的对象:")
    for key, value in decoded.items():
        print(f"  {key}: {value}")
    
    print()
    print("=== 使用指纹的示例 ===")
    
    # 使用设备指纹的示例
    x_s_common_fp = generator.generate_x_s_common(
        platform="PC",
        url="https://edith.xiaohongshu.com/api/sns/web/v1/feed",
        b1="original_b1",
        b1b1="1", 
        a1="sample_a1_value",
        use_fingerprint=True,
        fingerprint_data="fingerprint_12345"
    )
    
    print(f"使用指纹的 x-s-common: {x_s_common_fp}")
    
    decoded_fp = generator.decode_x_s_common(x_s_common_fp)
    print("解码后的对象 (使用指纹):")
    for key, value in decoded_fp.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
