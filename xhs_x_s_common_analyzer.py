#!/usr/bin/env python3
"""
小红书x-s-common专用分析器
专门针对小红书的JavaScript文件进行x-s-common相关代码的深度分析
"""

import os
import re
import json
import argparse
from pathlib import Path
import ast


class XHSXSCommonAnalyzer:
    def __init__(self, js_dir="xhsJsFiles", output_dir="xhs_x_s_common_analysis"):
        """
        初始化小红书x-s-common分析器
        
        Args:
            js_dir (str): JavaScript文件目录
            output_dir (str): 分析结果输出目录
        """
        self.js_dir = Path(js_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 小红书特有的模式
        self.xhs_patterns = {
            'x_s_common_variants': [
                r'["\']x-s-common["\']',
                r'X-S-Common',
                r'x_s_common',
                r'xscommon',
                r'XSCommon',
                r'X_S_COMMON',
                r'xs_common',
                r'XS_COMMON'
            ],
            'sign_functions': [
                r'function\s+\w*[Ss]ign\w*\s*\(',
                r'\w*[Ss]ign\w*\s*[:=]\s*function',
                r'\w*[Ss]ign\w*\s*[:=]\s*\([^)]*\)\s*=>',
                r'sign\s*:\s*function',
                r'getSign\s*[:=]',
                r'createSign\s*[:=]',
                r'generateSign\s*[:=]'
            ],
            'encrypt_functions': [
                r'function\s+\w*[Ee]ncrypt\w*\s*\(',
                r'\w*[Ee]ncrypt\w*\s*[:=]\s*function',
                r'encrypt\s*:\s*function',
                r'getEncrypt\s*[:=]',
                r'createEncrypt\s*[:=]'
            ],
            'header_operations': [
                r'setRequestHeader\s*\([^)]*["\']x-s-common["\']',
                r'headers\s*\[[^]]*["\']x-s-common["\']',
                r'headers\s*\.\s*["\']x-s-common["\']',
                r'["\']x-s-common["\']:\s*[^,}]+',
                r'common\s*:\s*[^,}]+',
                r'X_S_COMMON\s*:\s*[^,}]+'
            ],
            'crypto_operations': [
                r'md5\s*\(',
                r'MD5\s*\(',
                r'sha1\s*\(',
                r'SHA1\s*\(',
                r'sha256\s*\(',
                r'SHA256\s*\(',
                r'hmac\s*\(',
                r'HMAC\s*\(',
                r'CryptoJS\.',
                r'crypto-js',
                r'btoa\s*\(',
                r'atob\s*\(',
                r'base64\.',
                r'Base64\.'
            ],
            'timestamp_operations': [
                r'Date\.now\s*\(\)',
                r'new\s+Date\s*\(\)\.getTime\s*\(\)',
                r'getTime\s*\(\)',
                r'timestamp\s*[:=]',
                r'time\s*[:=]',
                r'\+new\s+Date',
                r'Math\.floor\s*\([^)]*Date',
                r'parseInt\s*\([^)]*Date'
            ],
            'url_path_operations': [
                r'pathname',
                r'location\.pathname',
                r'window\.location\.pathname',
                r'url\.pathname',
                r'path\s*[:=]',
                r'getPath\s*\(',
                r'parsePath\s*\('
            ],
            'request_data_operations': [
                r'JSON\.stringify\s*\(',
                r'stringify\s*\(',
                r'data\s*[:=]',
                r'body\s*[:=]',
                r'payload\s*[:=]',
                r'params\s*[:=]'
            ]
        }
        
        self.results = {
            'files_analyzed': [],
            'pattern_matches': {},
            'extracted_functions': [],
            'potential_x_s_common_generators': [],
            'crypto_chains': [],
            'analysis_summary': {}
        }
    
    def read_js_file(self, file_path):
        """读取JavaScript文件内容"""
        encodings = ['utf-8', 'gbk', 'latin-1']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文件失败 {file_path}: {e}")
                return None
        print(f"无法以任何编码读取文件: {file_path}")
        return None
    
    def find_xhs_patterns(self, content, file_path):
        """查找小红书特有的模式"""
        file_matches = {}
        
        for category, patterns in self.xhs_patterns.items():
            matches = []
            for pattern in patterns:
                found = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                for match in found:
                    # 获取更大的上下文
                    start = max(0, match.start() - 200)
                    end = min(len(content), match.end() + 200)
                    context = content[start:end]
                    
                    matches.append({
                        'pattern': pattern,
                        'match': match.group(),
                        'start': match.start(),
                        'end': match.end(),
                        'context': context.strip(),
                        'line_number': content[:match.start()].count('\n') + 1
                    })
            
            if matches:
                file_matches[category] = matches
        
        return file_matches
    
    def extract_function_with_context(self, content, match_pos, context_size=3000):
        """提取函数及其上下文"""
        # 向前查找函数开始
        start_search = max(0, match_pos - context_size)
        end_search = min(len(content), match_pos + context_size)
        
        search_area = content[start_search:end_search]
        
        # 查找函数定义模式
        function_patterns = [
            r'function\s+(\w+)\s*\([^)]*\)\s*\{',
            r'(\w+)\s*[:=]\s*function\s*\([^)]*\)\s*\{',
            r'(\w+)\s*[:=]\s*\([^)]*\)\s*=>\s*\{',
            r'async\s+function\s+(\w+)\s*\([^)]*\)\s*\{',
            r'(\w+)\s*:\s*function\s*\([^)]*\)\s*\{',
            r'(\w+)\s*:\s*\([^)]*\)\s*=>\s*\{'
        ]
        
        functions = []
        for pattern in function_patterns:
            matches = re.finditer(pattern, search_area, re.IGNORECASE)
            for func_match in matches:
                func_start = start_search + func_match.start()
                func_name = func_match.group(1) if func_match.groups() else "anonymous"
                
                # 提取完整函数体
                func_body = self.extract_complete_function_body(content, func_start)
                if func_body and len(func_body) > 100:
                    functions.append({
                        'name': func_name,
                        'start_pos': func_start,
                        'body': func_body,
                        'line_number': content[:func_start].count('\n') + 1
                    })
        
        return functions
    
    def extract_complete_function_body(self, content, start_pos):
        """提取完整的函数体"""
        brace_count = 0
        paren_count = 0
        in_string = False
        string_char = None
        escape_next = False
        
        # 找到第一个 {
        i = start_pos
        while i < len(content) and content[i] != '{':
            i += 1
        
        if i >= len(content):
            return None
        
        func_start = i
        i += 1
        brace_count = 1
        
        while i < len(content) and brace_count > 0:
            char = content[i]
            
            if escape_next:
                escape_next = False
            elif char == '\\':
                escape_next = True
            elif not in_string:
                if char in ['"', "'", '`']:
                    in_string = True
                    string_char = char
                elif char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
            else:
                if char == string_char:
                    in_string = False
                    string_char = None
            
            i += 1
        
        if brace_count == 0:
            # 向前查找函数声明开始
            j = func_start - 1
            while j >= 0 and content[j] not in ['\n', ';', '}']:
                j -= 1
            
            return content[j+1:i]
        
        return None

    def analyze_crypto_chain(self, content, matches):
        """分析加密链路"""
        crypto_chains = []

        # 查找可能的加密流程
        for category, category_matches in matches.items():
            if category in ['crypto_operations', 'timestamp_operations', 'url_path_operations']:
                for match in category_matches:
                    # 在匹配点附近查找相关的加密操作
                    start = max(0, match['start'] - 1000)
                    end = min(len(content), match['end'] + 1000)
                    context = content[start:end]

                    # 查找加密相关的变量和函数调用
                    crypto_elements = []

                    # 查找变量赋值
                    var_patterns = [
                        r'(\w+)\s*=\s*[^;]+(?:md5|sha|hmac|encrypt|sign)',
                        r'(\w+)\s*=\s*Date\.now\(\)',
                        r'(\w+)\s*=\s*[^;]+\.pathname',
                        r'(\w+)\s*=\s*JSON\.stringify'
                    ]

                    for pattern in var_patterns:
                        var_matches = re.finditer(pattern, context, re.IGNORECASE)
                        for var_match in var_matches:
                            crypto_elements.append({
                                'type': 'variable',
                                'name': var_match.group(1),
                                'operation': var_match.group(0)
                            })

                    if crypto_elements:
                        crypto_chains.append({
                            'trigger_match': match['match'],
                            'file_position': match['start'],
                            'elements': crypto_elements,
                            'context': context[:500]  # 限制上下文长度
                        })

        return crypto_chains

    def identify_x_s_common_generators(self, content, matches):
        """识别可能的x-s-common生成函数"""
        generators = []

        # 查找包含多种相关模式的函数
        for category, category_matches in matches.items():
            for match in category_matches:
                functions = self.extract_function_with_context(content, match['start'])

                for func in functions:
                    # 计算函数中包含的相关模式数量
                    pattern_count = 0
                    found_patterns = []

                    for cat, patterns in self.xhs_patterns.items():
                        for pattern in patterns:
                            if re.search(pattern, func['body'], re.IGNORECASE):
                                pattern_count += 1
                                found_patterns.append(f"{cat}: {pattern}")

                    # 如果函数包含多种相关模式，可能是x-s-common生成器
                    if pattern_count >= 3:
                        generators.append({
                            'function_name': func['name'],
                            'line_number': func['line_number'],
                            'pattern_count': pattern_count,
                            'found_patterns': found_patterns,
                            'function_body': func['body'],
                            'confidence': min(pattern_count * 20, 100)  # 置信度
                        })

        # 按置信度排序
        generators.sort(key=lambda x: x['confidence'], reverse=True)
        return generators

    def analyze_file(self, file_path):
        """分析单个JavaScript文件"""
        print(f"正在分析: {file_path.name}")

        content = self.read_js_file(file_path)
        if not content:
            return

        self.results['files_analyzed'].append(str(file_path))

        # 查找模式匹配
        matches = self.find_xhs_patterns(content, file_path)
        if matches:
            self.results['pattern_matches'][str(file_path)] = matches

            # 提取相关函数
            all_functions = []
            for category, category_matches in matches.items():
                for match in category_matches:
                    functions = self.extract_function_with_context(content, match['start'])
                    for func in functions:
                        func['file'] = str(file_path)
                        func['trigger_category'] = category
                        func['trigger_match'] = match['match']
                        all_functions.append(func)

            self.results['extracted_functions'].extend(all_functions)

            # 分析加密链路
            crypto_chains = self.analyze_crypto_chain(content, matches)
            self.results['crypto_chains'].extend(crypto_chains)

            # 识别x-s-common生成器
            generators = self.identify_x_s_common_generators(content, matches)
            for gen in generators:
                gen['file'] = str(file_path)
            self.results['potential_x_s_common_generators'].extend(generators)

            total_matches = sum(len(m) for m in matches.values())
            print(f"  发现 {total_matches} 个匹配项")
            print(f"  提取 {len(all_functions)} 个相关函数")
            print(f"  识别 {len(generators)} 个潜在生成器")

    def analyze_directory(self):
        """分析目录中的所有JavaScript文件"""
        if not self.js_dir.exists():
            print(f"目录不存在: {self.js_dir}")
            return

        js_files = list(self.js_dir.glob("*.js"))

        print(f"在 {self.js_dir} 中找到 {len(js_files)} 个JavaScript文件")
        print("-" * 60)

        for file_path in js_files:
            self.analyze_file(file_path)

        # 生成分析摘要
        self.generate_analysis_summary()

        print("-" * 60)
        print(f"分析完成！")
        print(f"  分析文件数: {len(self.results['files_analyzed'])}")
        print(f"  发现匹配的文件数: {len(self.results['pattern_matches'])}")
        print(f"  提取的函数数: {len(self.results['extracted_functions'])}")
        print(f"  潜在生成器数: {len(self.results['potential_x_s_common_generators'])}")

    def generate_analysis_summary(self):
        """生成分析摘要"""
        summary = {
            'total_files': len(self.results['files_analyzed']),
            'files_with_matches': len(self.results['pattern_matches']),
            'total_functions': len(self.results['extracted_functions']),
            'potential_generators': len(self.results['potential_x_s_common_generators']),
            'pattern_statistics': {},
            'top_generators': []
        }

        # 统计各类模式的出现次数
        for file_path, matches in self.results['pattern_matches'].items():
            for category, category_matches in matches.items():
                if category not in summary['pattern_statistics']:
                    summary['pattern_statistics'][category] = 0
                summary['pattern_statistics'][category] += len(category_matches)

        # 获取置信度最高的生成器
        if self.results['potential_x_s_common_generators']:
            summary['top_generators'] = self.results['potential_x_s_common_generators'][:5]

        self.results['analysis_summary'] = summary

    def save_results(self):
        """保存分析结果"""
        # 保存完整的JSON结果
        json_file = self.output_dir / "xhs_analysis_results.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

        # 保存潜在的x-s-common生成器
        generators_file = self.output_dir / "potential_x_s_common_generators.js"
        with open(generators_file, 'w', encoding='utf-8') as f:
            f.write("// 小红书潜在的x-s-common生成器函数\n")
            f.write("// " + "="*60 + "\n\n")

            for i, gen in enumerate(self.results['potential_x_s_common_generators']):
                f.write(f"// 生成器 #{i+1}\n")
                f.write(f"// 文件: {gen['file']}\n")
                f.write(f"// 函数名: {gen['function_name']}\n")
                f.write(f"// 行号: {gen['line_number']}\n")
                f.write(f"// 置信度: {gen['confidence']}%\n")
                f.write(f"// 匹配模式数: {gen['pattern_count']}\n")
                f.write("// 匹配的模式:\n")
                for pattern in gen['found_patterns'][:10]:  # 只显示前10个
                    f.write(f"//   - {pattern}\n")
                f.write("// " + "-"*40 + "\n")
                f.write(gen['function_body'])
                f.write("\n\n" + "="*80 + "\n\n")

        # 保存所有提取的函数
        all_functions_file = self.output_dir / "all_extracted_functions.js"
        with open(all_functions_file, 'w', encoding='utf-8') as f:
            f.write("// 所有提取的相关函数\n")
            f.write("// " + "="*60 + "\n\n")

            for i, func in enumerate(self.results['extracted_functions']):
                f.write(f"// 函数 #{i+1}\n")
                f.write(f"// 文件: {func['file']}\n")
                f.write(f"// 函数名: {func['name']}\n")
                f.write(f"// 行号: {func['line_number']}\n")
                f.write(f"// 触发类别: {func['trigger_category']}\n")
                f.write(f"// 触发匹配: {func['trigger_match']}\n")
                f.write("// " + "-"*40 + "\n")
                f.write(func['body'])
                f.write("\n\n" + "="*80 + "\n\n")

        # 保存分析报告
        report_file = self.output_dir / "analysis_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 小红书 x-s-common 分析报告\n\n")

            summary = self.results['analysis_summary']
            f.write("## 分析摘要\n\n")
            f.write(f"- **分析文件数**: {summary['total_files']}\n")
            f.write(f"- **发现匹配的文件数**: {summary['files_with_matches']}\n")
            f.write(f"- **提取的函数数**: {summary['total_functions']}\n")
            f.write(f"- **潜在生成器数**: {summary['potential_generators']}\n\n")

            f.write("## 模式统计\n\n")
            for category, count in summary['pattern_statistics'].items():
                f.write(f"- **{category}**: {count} 个匹配\n")

            f.write("\n## 置信度最高的生成器\n\n")
            for i, gen in enumerate(summary['top_generators']):
                f.write(f"### {i+1}. {gen['function_name']}\n")
                f.write(f"- **文件**: {gen['file']}\n")
                f.write(f"- **置信度**: {gen['confidence']}%\n")
                f.write(f"- **匹配模式数**: {gen['pattern_count']}\n")
                f.write(f"- **行号**: {gen['line_number']}\n\n")

            f.write("## 文件详情\n\n")
            for file_path, matches in self.results['pattern_matches'].items():
                f.write(f"### {os.path.basename(file_path)}\n")
                for category, category_matches in matches.items():
                    f.write(f"- **{category}**: {len(category_matches)} 个匹配\n")
                f.write("\n")

        print(f"\n分析结果已保存到: {self.output_dir.absolute()}")
        print(f"  - 完整结果: {json_file}")
        print(f"  - 潜在生成器: {generators_file}")
        print(f"  - 所有函数: {all_functions_file}")
        print(f"  - 分析报告: {report_file}")


def main():
    parser = argparse.ArgumentParser(description='小红书x-s-common专用分析器')
    parser.add_argument('-d', '--directory', default='xhsJsFiles',
                       help='JavaScript文件目录 (默认: xhsJsFiles)')
    parser.add_argument('-o', '--output', default='xhs_x_s_common_analysis',
                       help='输出目录 (默认: xhs_x_s_common_analysis)')

    args = parser.parse_args()

    print("小红书 x-s-common 分析器")
    print("="*60)

    analyzer = XHSXSCommonAnalyzer(args.directory, args.output)
    analyzer.analyze_directory()
    analyzer.save_results()


if __name__ == "__main__":
    main()
