#!/usr/bin/env python3
"""
浏览器开发者工具辅助脚本
用于快速提取和分析浏览器网络请求中的 x-s-common
"""

import json
import re
import base64
from urllib.parse import unquote
import pyperclip  # 需要安装: pip install pyperclip


class BrowserHelper:
    def __init__(self):
        """初始化浏览器辅助工具"""
        pass
    
    def extract_from_network_copy(self, network_data):
        """
        从浏览器网络面板复制的数据中提取 x-s-common
        支持多种格式：curl命令、HAR数据、请求头文本等
        
        Args:
            network_data (str): 从浏览器复制的网络请求数据
            
        Returns:
            dict: 提取的信息
        """
        result = {
            'source_type': 'unknown',
            'x_s_common': None,
            'url': None,
            'method': None,
            'all_headers': {},
            'decoded_info': None
        }
        
        # 检测数据类型并提取
        if network_data.strip().startswith('curl'):
            result.update(self._extract_from_curl(network_data))
        elif 'x-s-common' in network_data.lower():
            result.update(self._extract_from_headers_text(network_data))
        elif network_data.strip().startswith('{'):
            try:
                json_data = json.loads(network_data)
                result.update(self._extract_from_json(json_data))
            except json.JSONDecodeError:
                pass
        
        # 如果找到了 x-s-common，进行解码分析
        if result['x_s_common']:
            result['decoded_info'] = self._decode_x_s_common(result['x_s_common'])
        
        return result
    
    def _extract_from_curl(self, curl_command):
        """从curl命令中提取信息"""
        result = {'source_type': 'curl'}
        
        # 提取URL
        url_match = re.search(r"curl\s+['\"]?([^'\"\s]+)", curl_command)
        if url_match:
            result['url'] = url_match.group(1)
        
        # 提取方法
        method_match = re.search(r'-X\s+(\w+)', curl_command)
        result['method'] = method_match.group(1) if method_match else 'GET'
        
        # 提取所有请求头
        header_pattern = r"-H\s+['\"]([^:]+):\s*([^'\"]+)['\"]"
        headers = re.findall(header_pattern, curl_command)
        
        for name, value in headers:
            result['all_headers'][name] = value.strip()
            if name.lower() == 'x-s-common':
                result['x_s_common'] = value.strip()
        
        return result
    
    def _extract_from_headers_text(self, headers_text):
        """从请求头文本中提取信息"""
        result = {'source_type': 'headers_text'}
        
        lines = headers_text.split('\n')
        for line in lines:
            if ':' in line:
                name, value = line.split(':', 1)
                name = name.strip()
                value = value.strip()
                result['all_headers'][name] = value
                
                if name.lower() == 'x-s-common':
                    result['x_s_common'] = value
        
        return result
    
    def _extract_from_json(self, json_data):
        """从JSON数据中提取信息（如HAR格式）"""
        result = {'source_type': 'json'}
        
        # 尝试解析HAR格式
        if 'log' in json_data and 'entries' in json_data['log']:
            entries = json_data['log']['entries']
            for entry in entries:
                request = entry.get('request', {})
                headers = request.get('headers', [])
                
                for header in headers:
                    name = header.get('name', '')
                    value = header.get('value', '')
                    result['all_headers'][name] = value
                    
                    if name.lower() == 'x-s-common':
                        result['x_s_common'] = value
                        result['url'] = request.get('url')
                        result['method'] = request.get('method')
                        break
                
                if result['x_s_common']:
                    break
        
        return result
    
    def _decode_x_s_common(self, x_s_common_value):
        """解码 x-s-common 值"""
        decoded_info = {
            'original': x_s_common_value,
            'length': len(x_s_common_value),
            'base64_decoded': None,
            'json_decoded': None,
            'url_decoded': None,
            'hex_decoded': None,
            'analysis': {}
        }
        
        # Base64 解码
        try:
            base64_decoded = base64.b64decode(x_s_common_value + '==').decode('utf-8')
            decoded_info['base64_decoded'] = base64_decoded
            
            # 尝试JSON解析
            try:
                json_decoded = json.loads(base64_decoded)
                decoded_info['json_decoded'] = json_decoded
            except json.JSONDecodeError:
                pass
        except Exception:
            pass
        
        # URL 解码
        try:
            url_decoded = unquote(x_s_common_value)
            if url_decoded != x_s_common_value:
                decoded_info['url_decoded'] = url_decoded
        except Exception:
            pass
        
        # 十六进制解码
        try:
            if all(c in '0123456789abcdefABCDEF' for c in x_s_common_value) and len(x_s_common_value) % 2 == 0:
                hex_decoded = bytes.fromhex(x_s_common_value).decode('utf-8')
                decoded_info['hex_decoded'] = hex_decoded
        except Exception:
            pass
        
        # 分析特征
        decoded_info['analysis'] = {
            'contains_timestamp': bool(re.search(r'\d{10,13}', x_s_common_value)),
            'contains_equals': '=' in x_s_common_value,
            'contains_plus': '+' in x_s_common_value,
            'contains_slash': '/' in x_s_common_value,
            'is_base64_like': self._is_base64_like(x_s_common_value),
            'is_hex_like': all(c in '0123456789abcdefABCDEF' for c in x_s_common_value),
            'possible_encoding': self._guess_encoding(x_s_common_value)
        }
        
        return decoded_info
    
    def _is_base64_like(self, value):
        """检查是否像Base64编码"""
        if len(value) % 4 != 0:
            return False
        return all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in value)
    
    def _guess_encoding(self, value):
        """猜测编码类型"""
        if self._is_base64_like(value):
            return 'base64'
        elif all(c in '0123456789abcdefABCDEF' for c in value):
            return 'hex'
        elif '%' in value:
            return 'url_encoded'
        else:
            return 'unknown'
    
    def get_from_clipboard(self):
        """从剪贴板获取数据并分析"""
        try:
            clipboard_data = pyperclip.paste()
            if not clipboard_data:
                print("剪贴板为空")
                return None
            
            print(f"从剪贴板获取到 {len(clipboard_data)} 个字符的数据")
            return self.extract_from_network_copy(clipboard_data)
        except Exception as e:
            print(f"读取剪贴板失败: {e}")
            return None
    
    def interactive_mode(self):
        """交互模式"""
        print("=== x-s-common 提取工具 - 交互模式 ===")
        print("支持的输入格式:")
        print("1. curl 命令")
        print("2. 请求头文本")
        print("3. HAR JSON 数据")
        print("4. 从剪贴板读取 (输入 'clipboard')")
        print("5. 退出 (输入 'quit')")
        print()
        
        while True:
            print("-" * 50)
            user_input = input("请输入数据 (或命令): ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'clipboard':
                result = self.get_from_clipboard()
            else:
                result = self.extract_from_network_copy(user_input)
            
            if result and result.get('x_s_common'):
                self._display_result(result)
            else:
                print("未找到 x-s-common 或解析失败")
    
    def _display_result(self, result):
        """显示分析结果"""
        print("\n=== 分析结果 ===")
        print(f"数据来源: {result['source_type']}")
        
        if result.get('url'):
            print(f"URL: {result['url']}")
        if result.get('method'):
            print(f"方法: {result['method']}")
        
        print(f"\nx-s-common: {result['x_s_common']}")
        
        if result.get('decoded_info'):
            decoded = result['decoded_info']
            print(f"长度: {decoded['length']}")
            
            if decoded.get('base64_decoded'):
                print(f"Base64解码: {decoded['base64_decoded']}")
            
            if decoded.get('json_decoded'):
                print(f"JSON解析: {json.dumps(decoded['json_decoded'], ensure_ascii=False, indent=2)}")
            
            if decoded.get('url_decoded'):
                print(f"URL解码: {decoded['url_decoded']}")
            
            if decoded.get('hex_decoded'):
                print(f"十六进制解码: {decoded['hex_decoded']}")
            
            analysis = decoded.get('analysis', {})
            print(f"编码类型推测: {analysis.get('possible_encoding', 'unknown')}")
            
            if analysis.get('contains_timestamp'):
                print("包含时间戳特征")
        
        # 显示其他重要请求头
        important_headers = ['referer', 'user-agent', 'cookie', 'authorization']
        print("\n=== 其他重要请求头 ===")
        for header_name, header_value in result.get('all_headers', {}).items():
            if header_name.lower() in important_headers:
                print(f"{header_name}: {header_value[:100]}{'...' if len(header_value) > 100 else ''}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='浏览器开发者工具辅助脚本')
    parser.add_argument('--interactive', '-i', action='store_true', help='启动交互模式')
    parser.add_argument('--clipboard', '-c', action='store_true', help='从剪贴板读取数据')
    parser.add_argument('--file', '-f', help='从文件读取数据')
    
    args = parser.parse_args()
    
    helper = BrowserHelper()
    
    if args.interactive:
        helper.interactive_mode()
    elif args.clipboard:
        result = helper.get_from_clipboard()
        if result:
            helper._display_result(result)
    elif args.file:
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                data = f.read()
            result = helper.extract_from_network_copy(data)
            if result:
                helper._display_result(result)
        except Exception as e:
            print(f"读取文件失败: {e}")
    else:
        print("请使用 --interactive, --clipboard 或 --file 参数")
        print("或者运行: python browser_helper.py --help")


if __name__ == "__main__":
    main()
