#!/usr/bin/env python3
"""
JavaScript抓取脚本
用于抓取指定URL页面中的所有JavaScript代码
"""

import requests
import os
import re
import argparse
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import time
from pathlib import Path


class JavaScriptScraper:
    def __init__(self, base_url, output_dir="js_files", timeout=10):
        """
        初始化JavaScript抓取器
        
        Args:
            base_url (str): 目标网页URL
            output_dir (str): 输出目录
            timeout (int): 请求超时时间
        """
        self.base_url = base_url
        self.output_dir = Path(output_dir)
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
    def get_page_content(self):
        """获取页面内容"""
        try:
            print(f"正在获取页面: {self.base_url}")
            response = self.session.get(self.base_url, timeout=self.timeout)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            return response.text
        except requests.RequestException as e:
            print(f"获取页面失败: {e}")
            return None
    
    def extract_js_urls(self, html_content):
        """从HTML中提取JavaScript URL"""
        soup = BeautifulSoup(html_content, 'html.parser')
        js_urls = []
        inline_scripts = []
        
        # 查找所有script标签
        script_tags = soup.find_all('script')
        
        for i, script in enumerate(script_tags):
            # 外部JavaScript文件
            if script.get('src'):
                src = script.get('src')
                # 转换为绝对URL
                absolute_url = urljoin(self.base_url, src)
                js_urls.append(absolute_url)
                print(f"发现外部JS文件: {absolute_url}")
            
            # 内联JavaScript代码
            elif script.string and script.string.strip():
                inline_scripts.append({
                    'content': script.string,
                    'index': i
                })
                print(f"发现内联JS代码块 #{i+1}")
        
        return js_urls, inline_scripts
    
    def download_js_file(self, url, filename):
        """下载JavaScript文件"""
        try:
            print(f"正在下载: {url}")
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # 保存文件
            file_path = self.output_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"已保存: {file_path}")
            return True
            
        except requests.RequestException as e:
            print(f"下载失败 {url}: {e}")
            return False
        except Exception as e:
            print(f"保存文件失败 {filename}: {e}")
            return False
    
    def save_inline_script(self, script_content, index):
        """保存内联JavaScript代码"""
        filename = f"inline_script_{index+1}.js"
        file_path = self.output_dir / filename
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            print(f"已保存内联脚本: {file_path}")
            return True
        except Exception as e:
            print(f"保存内联脚本失败: {e}")
            return False
    
    def generate_filename(self, url):
        """根据URL生成文件名"""
        parsed_url = urlparse(url)
        path = parsed_url.path
        
        # 如果路径为空或只有斜杠，使用域名
        if not path or path == '/':
            filename = f"{parsed_url.netloc}.js"
        else:
            # 提取文件名
            filename = os.path.basename(path)
            if not filename.endswith('.js'):
                filename += '.js'
        
        # 清理文件名中的非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # 如果文件名已存在，添加序号
        counter = 1
        original_filename = filename
        while (self.output_dir / filename).exists():
            name, ext = os.path.splitext(original_filename)
            filename = f"{name}_{counter}{ext}"
            counter += 1
        
        return filename
    
    def scrape(self):
        """执行抓取任务"""
        print(f"开始抓取JavaScript文件...")
        print(f"目标URL: {self.base_url}")
        print(f"输出目录: {self.output_dir.absolute()}")
        print("-" * 50)
        
        # 获取页面内容
        html_content = self.get_page_content()
        if not html_content:
            print("无法获取页面内容，抓取失败")
            return
        
        # 提取JavaScript URLs和内联脚本
        js_urls, inline_scripts = self.extract_js_urls(html_content)
        
        print(f"\n找到 {len(js_urls)} 个外部JS文件，{len(inline_scripts)} 个内联脚本")
        print("-" * 50)
        
        # 下载外部JavaScript文件
        success_count = 0
        for url in js_urls:
            filename = self.generate_filename(url)
            if self.download_js_file(url, filename):
                success_count += 1
            time.sleep(0.5)  # 避免请求过于频繁
        
        # 保存内联脚本
        for script in inline_scripts:
            if self.save_inline_script(script['content'], script['index']):
                success_count += 1
        
        print("-" * 50)
        print(f"抓取完成！成功保存 {success_count} 个文件")
        print(f"文件保存在: {self.output_dir.absolute()}")


def main():
    parser = argparse.ArgumentParser(description='抓取网页中的所有JavaScript文件')
    parser.add_argument('url', help='目标网页URL')
    parser.add_argument('-o', '--output', default='js_files', help='输出目录 (默认: js_files)')
    parser.add_argument('-t', '--timeout', type=int, default=10, help='请求超时时间 (默认: 10秒)')
    
    args = parser.parse_args()
    
    # 验证URL格式
    if not args.url.startswith(('http://', 'https://')):
        args.url = 'https://' + args.url
    
    # 创建抓取器并执行
    scraper = JavaScriptScraper(args.url, args.output, args.timeout)
    scraper.scrape()


if __name__ == "__main__":
    main()
