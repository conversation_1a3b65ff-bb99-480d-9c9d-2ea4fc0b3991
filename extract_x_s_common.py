#!/usr/bin/env python3
"""
提取请求头 x-s-common 的脚本
支持多种方式获取和分析 x-s-common 请求头
"""

import requests
import json
import base64
import argparse
import time
from urllib.parse import urlparse, parse_qs
import re
from pathlib import Path


class XSCommonExtractor:
    def __init__(self, timeout=10):
        """
        初始化 x-s-common 提取器
        
        Args:
            timeout (int): 请求超时时间
        """
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置常用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        })
    
    def extract_from_request(self, url, method='GET', headers=None, data=None):
        """
        从实际请求中提取 x-s-common
        
        Args:
            url (str): 请求URL
            method (str): 请求方法
            headers (dict): 额外的请求头
            data (dict): 请求数据
            
        Returns:
            dict: 包含 x-s-common 和其他相关信息
        """
        try:
            print(f"正在请求: {url}")
            print(f"请求方法: {method}")
            
            # 合并请求头
            request_headers = self.session.headers.copy()
            if headers:
                request_headers.update(headers)
            
            # 发送请求
            if method.upper() == 'GET':
                response = self.session.get(url, headers=request_headers, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(url, headers=request_headers, json=data, timeout=self.timeout)
            else:
                response = self.session.request(method, url, headers=request_headers, json=data, timeout=self.timeout)
            
            # 提取响应信息
            result = {
                'url': url,
                'method': method,
                'status_code': response.status_code,
                'request_headers': dict(response.request.headers),
                'response_headers': dict(response.headers),
                'x_s_common': None,
                'x_s_common_decoded': None,
                'other_x_headers': {}
            }
            
            # 查找 x-s-common 在请求头中
            for key, value in response.request.headers.items():
                if key.lower() == 'x-s-common':
                    result['x_s_common'] = value
                    result['x_s_common_decoded'] = self.decode_x_s_common(value)
                    print(f"找到 x-s-common: {value}")
                elif key.lower().startswith('x-'):
                    result['other_x_headers'][key] = value
            
            # 查找响应头中的相关信息
            for key, value in response.headers.items():
                if key.lower().startswith('x-'):
                    if 'response_x_headers' not in result:
                        result['response_x_headers'] = {}
                    result['response_x_headers'][key] = value
            
            return result
            
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None
        except Exception as e:
            print(f"处理失败: {e}")
            return None
    
    def decode_x_s_common(self, x_s_common_value):
        """
        尝试解码 x-s-common 值
        
        Args:
            x_s_common_value (str): x-s-common 的值
            
        Returns:
            dict: 解码后的信息
        """
        if not x_s_common_value:
            return None
        
        decoded_info = {
            'original': x_s_common_value,
            'length': len(x_s_common_value),
            'base64_decoded': None,
            'json_decoded': None,
            'url_decoded': None,
            'analysis': {}
        }
        
        # 尝试 Base64 解码
        try:
            base64_decoded = base64.b64decode(x_s_common_value).decode('utf-8')
            decoded_info['base64_decoded'] = base64_decoded
            print(f"Base64 解码成功: {base64_decoded}")
            
            # 尝试解析为 JSON
            try:
                json_decoded = json.loads(base64_decoded)
                decoded_info['json_decoded'] = json_decoded
                print(f"JSON 解析成功: {json_decoded}")
            except json.JSONDecodeError:
                pass
                
        except Exception as e:
            print(f"Base64 解码失败: {e}")
        
        # 尝试 URL 解码
        try:
            from urllib.parse import unquote
            url_decoded = unquote(x_s_common_value)
            if url_decoded != x_s_common_value:
                decoded_info['url_decoded'] = url_decoded
                print(f"URL 解码: {url_decoded}")
        except Exception:
            pass
        
        # 分析字符串特征
        decoded_info['analysis'] = {
            'contains_equals': '=' in x_s_common_value,
            'contains_plus': '+' in x_s_common_value,
            'contains_slash': '/' in x_s_common_value,
            'is_hex': all(c in '0123456789abcdefABCDEF' for c in x_s_common_value),
            'pattern_analysis': self.analyze_pattern(x_s_common_value)
        }
        
        return decoded_info
    
    def analyze_pattern(self, value):
        """分析字符串模式"""
        patterns = {
            'timestamp': re.search(r'\d{10,13}', value),
            'uuid_like': re.search(r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', value, re.I),
            'base64_like': len(value) % 4 == 0 and all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in value),
            'hex_like': all(c in '0123456789abcdefABCDEF' for c in value) and len(value) % 2 == 0,
        }
        return {k: bool(v) for k, v in patterns.items()}
    
    def extract_from_har_file(self, har_file_path):
        """
        从 HAR 文件中提取 x-s-common
        
        Args:
            har_file_path (str): HAR 文件路径
            
        Returns:
            list: 包含所有找到的 x-s-common 信息
        """
        try:
            with open(har_file_path, 'r', encoding='utf-8') as f:
                har_data = json.load(f)
            
            results = []
            entries = har_data.get('log', {}).get('entries', [])
            
            for entry in entries:
                request = entry.get('request', {})
                headers = request.get('headers', [])
                
                x_s_common = None
                for header in headers:
                    if header.get('name', '').lower() == 'x-s-common':
                        x_s_common = header.get('value')
                        break
                
                if x_s_common:
                    result = {
                        'url': request.get('url'),
                        'method': request.get('method'),
                        'x_s_common': x_s_common,
                        'x_s_common_decoded': self.decode_x_s_common(x_s_common),
                        'timestamp': entry.get('startedDateTime'),
                        'all_headers': {h.get('name'): h.get('value') for h in headers}
                    }
                    results.append(result)
            
            print(f"从 HAR 文件中找到 {len(results)} 个包含 x-s-common 的请求")
            return results
            
        except Exception as e:
            print(f"解析 HAR 文件失败: {e}")
            return []
    
    def extract_from_curl_command(self, curl_command):
        """
        从 curl 命令中提取 x-s-common
        
        Args:
            curl_command (str): curl 命令字符串
            
        Returns:
            dict: 提取的信息
        """
        try:
            # 简单解析 curl 命令
            x_s_common_match = re.search(r'-H\s+["\']x-s-common:\s*([^"\']+)["\']', curl_command, re.I)
            url_match = re.search(r'curl\s+["\']?([^"\'\s]+)', curl_command)
            
            result = {
                'curl_command': curl_command,
                'url': url_match.group(1) if url_match else None,
                'x_s_common': None,
                'x_s_common_decoded': None
            }
            
            if x_s_common_match:
                x_s_common = x_s_common_match.group(1).strip()
                result['x_s_common'] = x_s_common
                result['x_s_common_decoded'] = self.decode_x_s_common(x_s_common)
                print(f"从 curl 命令中提取到 x-s-common: {x_s_common}")
            else:
                print("在 curl 命令中未找到 x-s-common")
            
            return result
            
        except Exception as e:
            print(f"解析 curl 命令失败: {e}")
            return None
    
    def save_results(self, results, output_file="x_s_common_results.json"):
        """保存结果到文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存结果失败: {e}")
    
    def monitor_requests(self, urls, interval=5, duration=60):
        """
        监控多个URL的请求，持续提取 x-s-common
        
        Args:
            urls (list): URL列表
            interval (int): 请求间隔（秒）
            duration (int): 监控持续时间（秒）
        """
        print(f"开始监控 {len(urls)} 个URL，持续 {duration} 秒，间隔 {interval} 秒")
        
        results = []
        start_time = time.time()
        
        while time.time() - start_time < duration:
            for url in urls:
                result = self.extract_from_request(url)
                if result and result.get('x_s_common'):
                    result['monitor_timestamp'] = time.time()
                    results.append(result)
                    print(f"监控到 x-s-common: {result['x_s_common'][:50]}...")
                
                time.sleep(interval / len(urls))
        
        print(f"监控完成，共收集到 {len(results)} 个包含 x-s-common 的请求")
        return results


def main():
    parser = argparse.ArgumentParser(description='提取请求头 x-s-common')
    parser.add_argument('--url', help='目标URL')
    parser.add_argument('--method', default='GET', help='请求方法 (默认: GET)')
    parser.add_argument('--har', help='HAR文件路径')
    parser.add_argument('--curl', help='curl命令字符串')
    parser.add_argument('--output', default='x_s_common_results.json', help='输出文件')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间')
    parser.add_argument('--monitor', nargs='+', help='监控模式，提供多个URL')
    parser.add_argument('--interval', type=int, default=5, help='监控间隔（秒）')
    parser.add_argument('--duration', type=int, default=60, help='监控持续时间（秒）')
    
    args = parser.parse_args()
    
    extractor = XSCommonExtractor(timeout=args.timeout)
    results = []
    
    if args.url:
        print(f"=== 从URL提取 x-s-common ===")
        result = extractor.extract_from_request(args.url, args.method)
        if result:
            results.append(result)
    
    elif args.har:
        print(f"=== 从HAR文件提取 x-s-common ===")
        results = extractor.extract_from_har_file(args.har)
    
    elif args.curl:
        print(f"=== 从curl命令提取 x-s-common ===")
        result = extractor.extract_from_curl_command(args.curl)
        if result:
            results.append(result)
    
    elif args.monitor:
        print(f"=== 监控模式 ===")
        results = extractor.monitor_requests(args.monitor, args.interval, args.duration)
    
    else:
        print("请提供 --url, --har, --curl 或 --monitor 参数")
        return
    
    # 显示结果
    if results:
        print("\n" + "="*50)
        print("提取结果:")
        for i, result in enumerate(results, 1):
            print(f"\n--- 结果 {i} ---")
            if result.get('x_s_common'):
                print(f"x-s-common: {result['x_s_common']}")
                if result.get('x_s_common_decoded'):
                    decoded = result['x_s_common_decoded']
                    if decoded.get('base64_decoded'):
                        print(f"Base64解码: {decoded['base64_decoded']}")
                    if decoded.get('json_decoded'):
                        print(f"JSON解析: {decoded['json_decoded']}")
            else:
                print("未找到 x-s-common")
        
        # 保存结果
        extractor.save_results(results, args.output)
    else:
        print("未找到任何 x-s-common 信息")


if __name__ == "__main__":
    main()
