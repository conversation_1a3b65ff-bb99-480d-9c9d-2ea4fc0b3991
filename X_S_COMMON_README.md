# x-s-common 提取工具

这是一个专门用于提取和分析请求头中 `x-s-common` 字段的Python工具集。主要用于分析小红书等网站的API请求。

## 📁 文件说明

- **`extract_x_s_common.py`** - 主要的提取脚本
- **`browser_helper.py`** - 浏览器开发者工具辅助脚本  
- **`x_s_common_examples.py`** - 使用示例
- **`requirements.txt`** - 依赖包列表

## 🚀 安装依赖

```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install requests beautifulsoup4 lxml pyperclip
```

## 📖 使用方法

### 1. 基本命令行使用

#### 从URL提取
```bash
python extract_x_s_common.py --url "https://www.xiaohongshu.com/api/sns/web/v1/search/notes"
```

#### 从HAR文件提取
```bash
python extract_x_s_common.py --har network_requests.har
```

#### 从curl命令提取
```bash
python extract_x_s_common.py --curl "curl 'https://example.com' -H 'x-s-common: eyJ0aW1lc3RhbXAi...'"
```

#### 监控模式
```bash
python extract_x_s_common.py --monitor "https://url1.com" "https://url2.com" --interval 5 --duration 60
```

### 2. 浏览器辅助工具

#### 交互模式
```bash
python browser_helper.py --interactive
```

#### 从剪贴板读取
```bash
python browser_helper.py --clipboard
```

#### 从文件读取
```bash
python browser_helper.py --file request_data.txt
```

## 🔧 功能特性

### extract_x_s_common.py 功能：
- ✅ 从实际HTTP请求中提取 x-s-common
- ✅ 从HAR文件批量提取
- ✅ 从curl命令解析提取
- ✅ 监控模式持续提取
- ✅ 自动解码Base64、JSON、URL编码
- ✅ 模式分析和特征识别
- ✅ 结果保存为JSON格式

### browser_helper.py 功能：
- ✅ 交互式操作界面
- ✅ 剪贴板数据自动识别
- ✅ 多种数据格式支持
- ✅ 智能编码类型推测
- ✅ 重要请求头提取

## 📋 实际使用步骤

### 方法1: 使用浏览器开发者工具

1. **打开目标网站**（如小红书）
2. **打开开发者工具** (F12)
3. **切换到Network面板**
4. **执行目标操作**（如搜索、浏览等）
5. **找到包含x-s-common的请求**
6. **右键复制为curl命令**
7. **运行提取脚本**：
   ```bash
   python extract_x_s_common.py --curl "粘贴的curl命令"
   ```

### 方法2: 使用HAR文件

1. **在Network面板中右键**
2. **选择"Save all as HAR with content"**
3. **保存HAR文件**
4. **运行提取脚本**：
   ```bash
   python extract_x_s_common.py --har saved_requests.har
   ```

### 方法3: 使用浏览器辅助工具

1. **复制请求数据到剪贴板**
2. **运行辅助工具**：
   ```bash
   python browser_helper.py --clipboard
   ```

## 🎯 输出示例

```json
{
  "url": "https://www.xiaohongshu.com/api/sns/web/v1/search/notes",
  "method": "GET",
  "status_code": 200,
  "x_s_common": "eyJ0aW1lc3RhbXAiOjE2MzQ1Njc4OTAsInNpZ24iOiJhYmMxMjMifQ==",
  "x_s_common_decoded": {
    "original": "eyJ0aW1lc3RhbXAiOjE2MzQ1Njc4OTAsInNpZ24iOiJhYmMxMjMifQ==",
    "length": 48,
    "base64_decoded": "{\"timestamp\":1634567890,\"sign\":\"abc123\"}",
    "json_decoded": {
      "timestamp": 1634567890,
      "sign": "abc123"
    },
    "analysis": {
      "contains_timestamp": true,
      "is_base64_like": true,
      "possible_encoding": "base64"
    }
  }
}
```

## 🔍 x-s-common 分析

### 常见编码格式：
1. **Base64编码的JSON** - 最常见
2. **URL编码的参数** - 如 `timestamp=xxx&sign=yyy`
3. **十六进制编码**
4. **简单字符串**

### 常见字段：
- `timestamp` - 时间戳
- `sign` - 签名
- `nonce` - 随机数
- `version` - 版本号
- `platform` - 平台标识

## ⚠️ 注意事项

1. **合法使用**: 仅用于学习和研究目的，遵守网站使用条款
2. **请求频率**: 避免过于频繁的请求，建议添加延迟
3. **动态值**: x-s-common通常是动态生成的，需要实时获取
4. **反爬机制**: 某些网站有反爬虫机制，注意请求头的完整性

## 🛠️ 高级用法

### 自定义请求头
```python
from extract_x_s_common import XSCommonExtractor

extractor = XSCommonExtractor()
headers = {
    'x-s-common': 'your_value_here',
    'Referer': 'https://www.xiaohongshu.com/',
    'User-Agent': 'Mozilla/5.0...'
}
result = extractor.extract_from_request(url, headers=headers)
```

### 批量处理
```python
urls = ['url1', 'url2', 'url3']
results = []
for url in urls:
    result = extractor.extract_from_request(url)
    if result:
        results.append(result)
```

## 🐛 故障排除

### 常见问题：

1. **找不到x-s-common**
   - 确认目标请求确实包含此请求头
   - 检查请求是否成功发送

2. **解码失败**
   - x-s-common可能使用了特殊编码
   - 尝试不同的解码方法

3. **请求失败**
   - 检查网络连接
   - 确认请求头的完整性
   - 可能需要登录状态或特殊权限

## 📞 扩展功能

如需更高级功能，可以考虑：
- 集成Selenium处理动态内容
- 添加代理池支持
- 实现自动签名算法逆向
- 添加GUI界面
- 支持更多网站的特殊格式
