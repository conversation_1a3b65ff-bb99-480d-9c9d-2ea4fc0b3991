#!/usr/bin/env python3
"""
x-s-common 提取脚本使用示例
"""

from extract_x_s_common import XSCommonExtractor
import json


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    extractor = XSCommonExtractor()
    
    # 示例1: 从URL提取
    url = "https://www.xiaohongshu.com/api/sns/web/v1/search/notes"
    headers = {
        'x-s-common': 'example_value_here',  # 这里需要真实的值
        'Referer': 'https://www.xiaohongshu.com/'
    }
    
    result = extractor.extract_from_request(url, headers=headers)
    if result:
        print(f"提取结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


def example_decode_x_s_common():
    """解码 x-s-common 示例"""
    print("\n=== 解码 x-s-common 示例 ===")
    
    extractor = XSCommonExtractor()
    
    # 示例 x-s-common 值（这些是示例，实际值会不同）
    example_values = [
        "eyJ0aW1lc3RhbXAiOjE2MzQ1Njc4OTAsInNpZ24iOiJhYmMxMjMifQ==",  # Base64编码的JSON
        "timestamp=1634567890&sign=abc123",  # URL编码格式
        "abc123def456",  # 简单字符串
    ]
    
    for i, value in enumerate(example_values, 1):
        print(f"\n--- 示例 {i} ---")
        print(f"原始值: {value}")
        decoded = extractor.decode_x_s_common(value)
        if decoded:
            print(f"解码信息: {json.dumps(decoded, ensure_ascii=False, indent=2)}")


def example_har_extraction():
    """从HAR文件提取示例"""
    print("\n=== HAR文件提取示例 ===")
    
    # 创建一个示例HAR文件
    sample_har = {
        "log": {
            "entries": [
                {
                    "request": {
                        "url": "https://www.xiaohongshu.com/api/sns/web/v1/search/notes",
                        "method": "GET",
                        "headers": [
                            {"name": "User-Agent", "value": "Mozilla/5.0..."},
                            {"name": "x-s-common", "value": "eyJ0aW1lc3RhbXAiOjE2MzQ1Njc4OTB9"},
                            {"name": "Referer", "value": "https://www.xiaohongshu.com/"}
                        ]
                    },
                    "startedDateTime": "2023-01-01T12:00:00.000Z"
                }
            ]
        }
    }
    
    # 保存示例HAR文件
    with open("sample.har", "w", encoding="utf-8") as f:
        json.dump(sample_har, f, ensure_ascii=False, indent=2)
    
    # 从HAR文件提取
    extractor = XSCommonExtractor()
    results = extractor.extract_from_har_file("sample.har")
    
    if results:
        print(f"从HAR文件提取到 {len(results)} 个结果")
        for result in results:
            print(f"URL: {result['url']}")
            print(f"x-s-common: {result['x_s_common']}")


def example_curl_extraction():
    """从curl命令提取示例"""
    print("\n=== curl命令提取示例 ===")
    
    extractor = XSCommonExtractor()
    
    # 示例curl命令
    curl_commands = [
        '''curl 'https://www.xiaohongshu.com/api/sns/web/v1/search/notes' \
  -H 'x-s-common: eyJ0aW1lc3RhbXAiOjE2MzQ1Njc4OTB9' \
  -H 'User-Agent: Mozilla/5.0...' ''',
        
        '''curl "https://example.com/api" -H "x-s-common: abc123def456"''',
    ]
    
    for i, curl_cmd in enumerate(curl_commands, 1):
        print(f"\n--- curl示例 {i} ---")
        result = extractor.extract_from_curl_command(curl_cmd)
        if result:
            print(f"提取结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


def example_monitoring():
    """监控模式示例"""
    print("\n=== 监控模式示例 ===")
    
    extractor = XSCommonExtractor()
    
    # 监控URL列表
    urls = [
        "https://www.xiaohongshu.com/api/sns/web/v1/search/notes",
        "https://www.xiaohongshu.com/api/sns/web/v1/user/info",
    ]
    
    print("开始监控（这是示例，实际需要真实的带有x-s-common的请求）")
    # results = extractor.monitor_requests(urls, interval=10, duration=30)
    # print(f"监控完成，收集到 {len(results)} 个结果")


def example_custom_headers():
    """自定义请求头示例"""
    print("\n=== 自定义请求头示例 ===")
    
    extractor = XSCommonExtractor()
    
    # 小红书常用的请求头
    xhs_headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'https://www.xiaohongshu.com/',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        # 这里需要真实的 x-s-common 值
        'x-s-common': 'your_real_x_s_common_value_here',
    }
    
    url = "https://www.xiaohongshu.com/api/sns/web/v1/search/notes"
    
    print("使用自定义请求头发送请求...")
    print("注意: 需要替换为真实的 x-s-common 值")
    
    # result = extractor.extract_from_request(url, headers=xhs_headers)
    # if result:
    #     print(f"请求成功: {result['status_code']}")


if __name__ == "__main__":
    print("x-s-common 提取脚本使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_decode_x_s_common()
    example_har_extraction()
    example_curl_extraction()
    example_custom_headers()
    
    print("\n" + "=" * 50)
    print("示例运行完成！")
    print("\n使用说明:")
    print("1. 替换示例中的 x-s-common 值为真实值")
    print("2. 根据需要修改目标URL")
    print("3. 可以使用浏览器开发者工具获取真实的请求头")
