#!/usr/bin/env python3
"""
xsCommon函数深度分析器
专门用于分析xsCommon函数的具体实现细节和依赖函数
"""

import re
import json
from pathlib import Path


class XSCommonDeepAnalyzer:
    def __init__(self, js_file_path="xhsJsFiles/vendor-dynamic.f0f5c43a.js"):
        """
        初始化xsCommon深度分析器
        
        Args:
            js_file_path (str): JavaScript文件路径
        """
        self.js_file_path = Path(js_file_path)
        self.content = self.read_js_file()
        self.analysis_results = {
            'xsCommon_function': None,
            'dependency_functions': {},
            'constants': {},
            'variable_mappings': {},
            'algorithm_analysis': {}
        }
    
    def read_js_file(self):
        """读取JavaScript文件内容"""
        try:
            with open(self.js_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None
    
    def find_xscommon_function(self):
        """查找并提取xsCommon函数的完整实现"""
        if not self.content:
            return None
        
        # 查找xsCommon函数定义
        pattern = r'function\s+xsCommon\s*\([^)]*\)\s*\{[^}]*\}'
        matches = re.finditer(pattern, self.content, re.IGNORECASE)
        
        xscommon_functions = []
        for match in matches:
            func_body = match.group(0)
            # 尝试提取完整的函数体（处理嵌套大括号）
            complete_func = self.extract_complete_function(match.start())
            if complete_func:
                xscommon_functions.append({
                    'simple_body': func_body,
                    'complete_body': complete_func,
                    'start_pos': match.start(),
                    'end_pos': match.end()
                })
        
        return xscommon_functions
    
    def extract_complete_function(self, start_pos):
        """提取完整的函数体（处理嵌套大括号）"""
        if not self.content:
            return None
        
        # 找到函数开始的{
        i = start_pos
        while i < len(self.content) and self.content[i] != '{':
            i += 1
        
        if i >= len(self.content):
            return None
        
        brace_count = 0
        func_start = i
        in_string = False
        string_char = None
        escape_next = False
        
        while i < len(self.content):
            char = self.content[i]
            
            if escape_next:
                escape_next = False
            elif char == '\\':
                escape_next = True
            elif not in_string:
                if char in ['"', "'", '`']:
                    in_string = True
                    string_char = char
                elif char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        # 找到函数结束，向前查找函数声明开始
                        j = func_start - 1
                        while j >= 0 and self.content[j] not in ['\n', ';', '}', ')']:
                            j -= 1
                        return self.content[j+1:i+1].strip()
            else:
                if char == string_char:
                    in_string = False
                    string_char = None
            
            i += 1
        
        return None
    
    def find_dependency_functions(self):
        """查找xsCommon函数的依赖函数"""
        dependencies = {}
        
        # 从xsCommon函数中提取的依赖函数名
        dependency_names = [
            'getPlatformCode', 'getSigCount', 'utils_shouldSign', 
            'b64Encode', 'encodeUtf8', 'O'
        ]
        
        for func_name in dependency_names:
            # 查找函数定义的多种模式
            patterns = [
                rf'function\s+{func_name}\s*\([^)]*\)\s*\{{[^}}]*\}}',
                rf'{func_name}\s*[:=]\s*function\s*\([^)]*\)\s*\{{[^}}]*\}}',
                rf'{func_name}\s*[:=]\s*\([^)]*\)\s*=>\s*\{{[^}}]*\}}',
                rf'var\s+{func_name}\s*=\s*function\s*\([^)]*\)\s*\{{[^}}]*\}}',
                rf'let\s+{func_name}\s*=\s*function\s*\([^)]*\)\s*\{{[^}}]*\}}',
                rf'const\s+{func_name}\s*=\s*function\s*\([^)]*\)\s*\{{[^}}]*\}}'
            ]
            
            found_functions = []
            for pattern in patterns:
                matches = re.finditer(pattern, self.content, re.IGNORECASE | re.DOTALL)
                for match in matches:
                    # 尝试提取完整函数
                    complete_func = self.extract_complete_function(match.start())
                    if complete_func:
                        found_functions.append({
                            'pattern_matched': pattern,
                            'simple_body': match.group(0),
                            'complete_body': complete_func,
                            'start_pos': match.start()
                        })
            
            if found_functions:
                dependencies[func_name] = found_functions
        
        return dependencies
    
    def find_constants_and_variables(self):
        """查找相关的常量和变量定义"""
        constants = {}
        
        # 查找可能的常量定义
        constant_patterns = [
            r'var\s+([A-Z_][A-Z0-9_]*)\s*=\s*([^;]+);',
            r'let\s+([A-Z_][A-Z0-9_]*)\s*=\s*([^;]+);',
            r'const\s+([A-Z_][A-Z0-9_]*)\s*=\s*([^;]+);',
            r'([A-Z_][A-Z0-9_]*)\s*=\s*([^;]+);'
        ]
        
        for pattern in constant_patterns:
            matches = re.finditer(pattern, self.content)
            for match in matches:
                var_name = match.group(1)
                var_value = match.group(2).strip()
                
                # 过滤一些明显的常量
                if len(var_name) <= 3 and var_name.isupper():
                    constants[var_name] = {
                        'value': var_value,
                        'position': match.start()
                    }
        
        return constants
    
    def analyze_xscommon_algorithm(self, xscommon_func):
        """分析xsCommon函数的算法逻辑"""
        if not xscommon_func:
            return {}
        
        func_body = xscommon_func.get('complete_body', '')
        
        analysis = {
            'parameters': self.extract_parameters(func_body),
            'local_variables': self.extract_local_variables(func_body),
            'object_structure': self.extract_object_structure(func_body),
            'key_operations': self.extract_key_operations(func_body),
            'return_logic': self.extract_return_logic(func_body)
        }
        
        return analysis
    
    def extract_parameters(self, func_body):
        """提取函数参数"""
        param_match = re.search(r'function\s+xsCommon\s*\(([^)]*)\)', func_body)
        if param_match:
            params = param_match.group(1).split(',')
            return [p.strip() for p in params if p.strip()]
        return []
    
    def extract_local_variables(self, func_body):
        """提取局部变量定义"""
        var_patterns = [
            r'var\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*([^;,]+)',
            r'let\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*([^;,]+)',
            r'const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*([^;,]+)'
        ]
        
        variables = {}
        for pattern in var_patterns:
            matches = re.finditer(pattern, func_body)
            for match in matches:
                var_name = match.group(1)
                var_value = match.group(2).strip()
                variables[var_name] = var_value
        
        return variables
    
    def extract_object_structure(self, func_body):
        """提取对象结构（特别是v对象）"""
        # 查找v对象的定义
        v_pattern = r'v\s*=\s*\{([^}]+)\}'
        match = re.search(v_pattern, func_body)
        
        if match:
            obj_content = match.group(1)
            # 解析对象属性
            prop_pattern = r'([a-zA-Z0-9_]+)\s*:\s*([^,}]+)'
            properties = {}
            
            for prop_match in re.finditer(prop_pattern, obj_content):
                prop_name = prop_match.group(1)
                prop_value = prop_match.group(2).strip()
                properties[prop_name] = prop_value
            
            return properties
        
        return {}
    
    def extract_key_operations(self, func_body):
        """提取关键操作"""
        operations = []
        
        # 查找关键操作模式
        operation_patterns = [
            r'localStorage\.getItem\([^)]+\)',
            r'JSON\.stringify\([^)]+\)',
            r'b64Encode\([^)]+\)',
            r'encodeUtf8\([^)]+\)',
            r'headers\[[^]]+\]\s*=\s*[^;]+',
            r'\.get\([^)]+\)',
            r'O\([^)]+\)'
        ]
        
        for pattern in operation_patterns:
            matches = re.finditer(pattern, func_body)
            for match in matches:
                operations.append({
                    'operation': match.group(0),
                    'type': self.classify_operation(match.group(0)),
                    'position': match.start()
                })
        
        return operations
    
    def classify_operation(self, operation):
        """分类操作类型"""
        if 'localStorage' in operation:
            return 'storage_access'
        elif 'JSON.stringify' in operation:
            return 'serialization'
        elif 'b64Encode' in operation or 'encodeUtf8' in operation:
            return 'encoding'
        elif 'headers' in operation:
            return 'header_setting'
        elif '.get(' in operation:
            return 'data_access'
        elif 'O(' in operation:
            return 'hash_or_encrypt'
        else:
            return 'unknown'
    
    def extract_return_logic(self, func_body):
        """提取返回逻辑"""
        return_patterns = [
            r'return\s+([^;]+);',
            r'headers\[[^]]+\]\s*=\s*([^;]+);'
        ]
        
        returns = []
        for pattern in return_patterns:
            matches = re.finditer(pattern, func_body)
            for match in matches:
                returns.append(match.group(1).strip())
        
        return returns
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始深度分析xsCommon函数...")
        
        # 1. 查找xsCommon函数
        xscommon_functions = self.find_xscommon_function()
        if xscommon_functions:
            self.analysis_results['xsCommon_function'] = xscommon_functions[0]  # 取第一个
            print(f"找到 {len(xscommon_functions)} 个xsCommon函数")
        else:
            print("未找到xsCommon函数")
            return
        
        # 2. 查找依赖函数
        dependencies = self.find_dependency_functions()
        self.analysis_results['dependency_functions'] = dependencies
        print(f"找到 {len(dependencies)} 个依赖函数")
        
        # 3. 查找常量
        constants = self.find_constants_and_variables()
        self.analysis_results['constants'] = constants
        print(f"找到 {len(constants)} 个可能的常量")
        
        # 4. 分析算法
        algorithm_analysis = self.analyze_xscommon_algorithm(self.analysis_results['xsCommon_function'])
        self.analysis_results['algorithm_analysis'] = algorithm_analysis
        print("完成算法分析")
        
        return self.analysis_results
    
    def save_analysis_results(self, output_file="xscommon_deep_analysis.json"):
        """保存分析结果"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
        print(f"分析结果已保存到: {output_file}")
    
    def generate_readable_report(self, output_file="xscommon_analysis_report.md"):
        """生成可读的分析报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# xsCommon函数深度分析报告\n\n")
            
            # xsCommon函数分析
            if self.analysis_results.get('xsCommon_function'):
                f.write("## xsCommon函数实现\n\n")
                func = self.analysis_results['xsCommon_function']
                f.write("```javascript\n")
                f.write(func.get('complete_body', ''))
                f.write("\n```\n\n")
            
            # 算法分析
            if self.analysis_results.get('algorithm_analysis'):
                algo = self.analysis_results['algorithm_analysis']
                f.write("## 算法分析\n\n")
                
                f.write("### 函数参数\n")
                for param in algo.get('parameters', []):
                    f.write(f"- `{param}`\n")
                f.write("\n")
                
                f.write("### 局部变量\n")
                for var_name, var_value in algo.get('local_variables', {}).items():
                    f.write(f"- `{var_name}` = `{var_value}`\n")
                f.write("\n")
                
                f.write("### 对象结构（v对象）\n")
                for prop_name, prop_value in algo.get('object_structure', {}).items():
                    f.write(f"- `{prop_name}`: `{prop_value}`\n")
                f.write("\n")
                
                f.write("### 关键操作\n")
                for op in algo.get('key_operations', []):
                    f.write(f"- **{op['type']}**: `{op['operation']}`\n")
                f.write("\n")
            
            # 依赖函数
            if self.analysis_results.get('dependency_functions'):
                f.write("## 依赖函数\n\n")
                for func_name, implementations in self.analysis_results['dependency_functions'].items():
                    f.write(f"### {func_name}\n")
                    for impl in implementations:
                        f.write("```javascript\n")
                        f.write(impl.get('complete_body', impl.get('simple_body', '')))
                        f.write("\n```\n\n")
        
        print(f"可读报告已保存到: {output_file}")


def main():
    analyzer = XSCommonDeepAnalyzer()
    results = analyzer.run_analysis()
    
    if results:
        analyzer.save_analysis_results()
        analyzer.generate_readable_report()
        
        print("\n=== 分析摘要 ===")
        if results.get('algorithm_analysis'):
            algo = results['algorithm_analysis']
            print(f"函数参数: {len(algo.get('parameters', []))}")
            print(f"局部变量: {len(algo.get('local_variables', {}))}")
            print(f"对象属性: {len(algo.get('object_structure', {}))}")
            print(f"关键操作: {len(algo.get('key_operations', []))}")


if __name__ == "__main__":
    main()
