#!/usr/bin/env python3
"""
提取JavaScript文件中x-s-common相关方法的脚本
专门用于分析JS文件中生成或处理x-s-common请求头的代码
"""

import os
import re
import json
import argparse
from pathlib import Path
from typing import List, Dict, Set


class XSCommonExtractor:
    def __init__(self, js_dir=".", output_dir="x_s_common_analysis"):
        """
        初始化x-s-common提取器
        
        Args:
            js_dir (str): JavaScript文件目录
            output_dir (str): 分析结果输出目录
        """
        self.js_dir = Path(js_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # x-s-common相关的关键词模式
        self.patterns = {
            'x_s_common_direct': [
                r'["\']x-s-common["\']',
                r'X-S-Common',
                r'x_s_common',
                r'xscommon',
                r'XSCommon'
            ],
            'header_setting': [
                r'setRequestHeader\s*\(\s*["\']x-s-common["\']',
                r'headers\s*\[\s*["\']x-s-common["\']',
                r'headers\s*:\s*\{[^}]*["\']x-s-common["\']',
                r'["\']x-s-common["\']:\s*[^,}]+',
            ],
            'function_names': [
                r'function\s+\w*[Xx][Ss][Cc]ommon\w*\s*\(',
                r'function\s+\w*[Gg]et[Xx][Ss]\w*\s*\(',
                r'function\s+\w*[Ss]ign\w*\s*\(',
                r'function\s+\w*[Ee]ncrypt\w*\s*\(',
                r'function\s+\w*[Hh]eader\w*\s*\(',
                r'\w*[Xx][Ss][Cc]ommon\w*\s*[:=]\s*function',
                r'\w*[Gg]et[Xx][Ss]\w*\s*[:=]\s*function',
                r'\w*[Ss]ign\w*\s*[:=]\s*function'
            ],
            'crypto_related': [
                r'md5\s*\(',
                r'sha\d*\s*\(',
                r'hmac\s*\(',
                r'encrypt\s*\(',
                r'encode\s*\(',
                r'btoa\s*\(',
                r'atob\s*\(',
                r'CryptoJS',
                r'crypto\.',
                r'digest\s*\(',
                r'hash\s*\('
            ],
            'timestamp_related': [
                r'Date\.now\s*\(\)',
                r'new\s+Date\s*\(\)',
                r'getTime\s*\(\)',
                r'timestamp',
                r'time\s*[:=]',
                r'\+new\s+Date'
            ]
        }
        
        self.results = {
            'files_analyzed': [],
            'matches_found': {},
            'extracted_functions': [],
            'suspicious_code_blocks': []
        }
    
    def read_js_file(self, file_path):
        """读取JavaScript文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    return f.read()
            except:
                print(f"无法读取文件: {file_path}")
                return None
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return None
    
    def find_pattern_matches(self, content, file_path):
        """在内容中查找模式匹配"""
        file_matches = {}
        
        for category, patterns in self.patterns.items():
            matches = []
            for pattern in patterns:
                found = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                for match in found:
                    # 获取匹配位置的上下文
                    start = max(0, match.start() - 100)
                    end = min(len(content), match.end() + 100)
                    context = content[start:end]
                    
                    matches.append({
                        'pattern': pattern,
                        'match': match.group(),
                        'start': match.start(),
                        'end': match.end(),
                        'context': context.strip(),
                        'line_number': content[:match.start()].count('\n') + 1
                    })
            
            if matches:
                file_matches[category] = matches
        
        return file_matches
    
    def extract_function_blocks(self, content, matches):
        """提取包含匹配项的函数块"""
        functions = []
        
        # 查找函数定义的正则表达式
        function_patterns = [
            r'function\s+\w+\s*\([^)]*\)\s*\{',
            r'\w+\s*[:=]\s*function\s*\([^)]*\)\s*\{',
            r'\w+\s*[:=]\s*\([^)]*\)\s*=>\s*\{',
            r'async\s+function\s+\w+\s*\([^)]*\)\s*\{',
            r'function\s*\([^)]*\)\s*\{'
        ]
        
        for category, category_matches in matches.items():
            for match in category_matches:
                # 在匹配位置附近查找函数定义
                search_start = max(0, match['start'] - 2000)
                search_end = min(len(content), match['end'] + 2000)
                search_area = content[search_start:search_end]
                
                for func_pattern in function_patterns:
                    func_matches = re.finditer(func_pattern, search_area, re.IGNORECASE)
                    for func_match in func_matches:
                        func_start = search_start + func_match.start()
                        
                        # 尝试找到完整的函数体
                        func_body = self.extract_complete_function(content, func_start)
                        if func_body and len(func_body) > 50:  # 过滤太短的代码块
                            functions.append({
                                'category': category,
                                'trigger_match': match['match'],
                                'function_start': func_start,
                                'function_body': func_body,
                                'line_number': content[:func_start].count('\n') + 1
                            })
        
        return functions

    def extract_complete_function(self, content, start_pos):
        """提取完整的函数体（处理大括号匹配）"""
        brace_count = 0
        in_function = False
        function_start = start_pos

        i = start_pos
        while i < len(content):
            char = content[i]

            if char == '{':
                if not in_function:
                    in_function = True
                    function_start = i
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if in_function and brace_count == 0:
                    # 找到函数结束
                    # 向前查找函数声明的开始
                    func_decl_start = function_start
                    j = function_start - 1
                    while j >= 0 and content[j] != '\n':
                        if content[j:j+8] == 'function' or content[j:j+5] == 'async':
                            func_decl_start = j
                            break
                        j -= 1

                    return content[func_decl_start:i+1]

            i += 1

        return None

    def analyze_file(self, file_path):
        """分析单个JavaScript文件"""
        print(f"正在分析: {file_path}")

        content = self.read_js_file(file_path)
        if not content:
            return

        self.results['files_analyzed'].append(str(file_path))

        # 查找模式匹配
        matches = self.find_pattern_matches(content, file_path)
        if matches:
            self.results['matches_found'][str(file_path)] = matches

            # 提取相关函数
            functions = self.extract_function_blocks(content, matches)
            for func in functions:
                func['file'] = str(file_path)
                self.results['extracted_functions'].append(func)

            print(f"  发现 {sum(len(m) for m in matches.values())} 个匹配项")
            print(f"  提取 {len(functions)} 个相关函数")

    def analyze_directory(self):
        """分析目录中的所有JavaScript文件"""
        js_files = []

        # 查找所有.js文件
        for file_path in self.js_dir.rglob("*.js"):
            js_files.append(file_path)

        print(f"找到 {len(js_files)} 个JavaScript文件")
        print("-" * 50)

        for file_path in js_files:
            self.analyze_file(file_path)

        print("-" * 50)
        print(f"分析完成！共分析 {len(js_files)} 个文件")

    def save_results(self):
        """保存分析结果"""
        # 保存完整的JSON结果
        json_file = self.output_dir / "analysis_results.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

        # 保存提取的函数代码
        functions_file = self.output_dir / "extracted_functions.js"
        with open(functions_file, 'w', encoding='utf-8') as f:
            f.write("// 提取的x-s-common相关函数\n")
            f.write("// " + "="*50 + "\n\n")

            for i, func in enumerate(self.results['extracted_functions']):
                f.write(f"// 函数 #{i+1}\n")
                f.write(f"// 文件: {func['file']}\n")
                f.write(f"// 行号: {func['line_number']}\n")
                f.write(f"// 类别: {func['category']}\n")
                f.write(f"// 触发匹配: {func['trigger_match']}\n")
                f.write("// " + "-"*30 + "\n")
                f.write(func['function_body'])
                f.write("\n\n" + "="*60 + "\n\n")

        # 保存匹配摘要
        summary_file = self.output_dir / "analysis_summary.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("X-S-Common 分析摘要\n")
            f.write("="*50 + "\n\n")

            f.write(f"分析文件数: {len(self.results['files_analyzed'])}\n")
            f.write(f"发现匹配的文件数: {len(self.results['matches_found'])}\n")
            f.write(f"提取的函数数: {len(self.results['extracted_functions'])}\n\n")

            f.write("匹配详情:\n")
            f.write("-"*30 + "\n")

            for file_path, matches in self.results['matches_found'].items():
                f.write(f"\n文件: {file_path}\n")
                for category, category_matches in matches.items():
                    f.write(f"  {category}: {len(category_matches)} 个匹配\n")
                    for match in category_matches[:3]:  # 只显示前3个
                        f.write(f"    - 行 {match['line_number']}: {match['match']}\n")

        print(f"分析结果已保存到: {self.output_dir.absolute()}")
        print(f"  - 完整结果: {json_file}")
        print(f"  - 提取的函数: {functions_file}")
        print(f"  - 分析摘要: {summary_file}")


def main():
    parser = argparse.ArgumentParser(description='提取JavaScript文件中的x-s-common相关方法')
    parser.add_argument('-d', '--directory', default='.', help='JavaScript文件目录 (默认: 当前目录)')
    parser.add_argument('-o', '--output', default='x_s_common_analysis', help='输出目录 (默认: x_s_common_analysis)')

    args = parser.parse_args()

    extractor = XSCommonExtractor(args.directory, args.output)
    extractor.analyze_directory()
    extractor.save_results()


if __name__ == "__main__":
    main()
