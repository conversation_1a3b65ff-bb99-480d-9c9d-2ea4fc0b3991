#!/usr/bin/env python3
"""
专门格式化x-s-common代码的脚本
"""

def format_x_s_common():
    """格式化x-s-common代码"""
    
    # 你提供的原始代码
    original_code = """{var i,a;try{var s=e.platform,u=r.url;if(S.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)}),!utils_shouldSign(u))return r;var c=r.headers["X-Sign"]||"",d=getSigCount(c),p=localStorage.getItem("b1"),f=localStorage.getItem("b1b1")||"1",v={s0:getPlatformCode(s),s1:"",x0:f,x1:C,x2:s||"PC",x3:"xhs-pc-web",x4:"4.68.0",x5:l.Z.get("a1"),x6:"",x7:"",x8:p,x9:O("".concat("").concat("").concat(p)),x10:d,x11:"normal"},h=k.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)});(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}"""
    
    # 手动格式化
    formatted_code = """function xsCommon(e, r) {
    var i, a;
    try {
        var s = e.platform,
            u = r.url;
        
        // 检查URL是否匹配某些模式
        if (S.map(function(e) {
            return new RegExp(e);
        }).some(function(e) {
            return e.test(u);
        }), !utils_shouldSign(u)) {
            return r;
        }
        
        // 收集基础数据
        var c = r.headers["X-Sign"] || "",
            d = getSigCount(c),
            p = localStorage.getItem("b1"),
            f = localStorage.getItem("b1b1") || "1";
        
        // 构建核心对象
        var v = {
            s0: getPlatformCode(s),     // 平台代码
            s1: "",                     // 空字符串
            x0: f,                      // b1b1值
            x1: C,                      // 常量C
            x2: s || "PC",              // 平台信息
            x3: "xhs-pc-web",           // 应用标识
            x4: "4.68.0",               // 版本号
            x5: l.Z.get("a1"),          // a1值
            x6: "",                     // 空字符串
            x7: "",                     // 空字符串
            x8: p,                      // b1值
            x9: O("".concat("").concat("").concat(p)),  // b1的哈希值
            x10: d,                     // 签名计数
            x11: "normal"               // 固定值
        };
        
        // 检查是否需要使用指纹
        var h = k.map(function(e) {
            return new RegExp(e);
        }).some(function(e) {
            return e.test(u);
        });
        
        // 根据指纹情况设置请求头
        if ((null === (i = window.xhsFingerprintV3) || void 0 === i ? void 0 : i.getCurMiniUa) && h) {
            // 异步获取指纹并设置
            null === (a = window.xhsFingerprintV3) || void 0 === a || a.getCurMiniUa(function(e) {
                v.x8 = e;  // 用指纹替换b1值
                v.x9 = O("".concat("").concat("").concat(e));  // 用指纹的哈希替换
                r.headers["X-S-Common"] = b64Encode(encodeUtf8(JSON.stringify(v)));
            });
        } else {
            // 直接设置请求头
            r.headers["X-S-Common"] = b64Encode(encodeUtf8(JSON.stringify(v)));
        }
        
    } catch (e) {
        // 忽略所有错误
    }
    
    return r;
}"""
    
    return formatted_code


def analyze_algorithm():
    """分析算法逻辑"""
    print("=== x-s-common 算法分析 ===")
    print()
    
    print("📋 函数签名:")
    print("function xsCommon(e, r)")
    print("  - e: 包含platform属性的对象")
    print("  - r: 请求对象，包含url和headers")
    print()
    
    print("🔍 核心流程:")
    print("1. 检查URL是否需要签名 (utils_shouldSign)")
    print("2. 收集基础数据 (X-Sign, localStorage等)")
    print("3. 构建14字段的对象v")
    print("4. 检查是否需要设备指纹")
    print("5. 生成x-s-common: JSON.stringify(v) → encodeUtf8 → b64Encode")
    print()
    
    print("📊 对象v的结构:")
    fields = [
        ("s0", "getPlatformCode(s)", "平台代码"),
        ("s1", '""', "空字符串"),
        ("x0", "f (b1b1值)", "localStorage['b1b1'] 或 '1'"),
        ("x1", "C", "常量C"),
        ("x2", 's || "PC"', "平台信息"),
        ("x3", '"xhs-pc-web"', "应用标识"),
        ("x4", '"4.68.0"', "版本号"),
        ("x5", 'l.Z.get("a1")', "a1值"),
        ("x6", '""', "空字符串"),
        ("x7", '""', "空字符串"),
        ("x8", "p (b1值或指纹)", "localStorage['b1'] 或设备指纹"),
        ("x9", "O(x8)", "x8的哈希值"),
        ("x10", "d", "签名计数"),
        ("x11", '"normal"', "固定值")
    ]
    
    for field, value, desc in fields:
        print(f"  {field:3}: {value:20} // {desc}")
    
    print()
    print("🔑 关键依赖:")
    print("  - getPlatformCode(): 平台代码映射")
    print("  - getSigCount(): 签名计数管理")
    print("  - utils_shouldSign(): URL签名判断")
    print("  - O(): 哈希函数 (可能是CRC32)")
    print("  - b64Encode(): Base64编码")
    print("  - encodeUtf8(): UTF8编码")
    print("  - 常量: C, l.Z, S, k")
    print()
    
    print("⚡ 动态逻辑:")
    print("  - 如果有xhsFingerprintV3且URL匹配k数组模式:")
    print("    → 异步获取设备指纹，替换x8和x9")
    print("  - 否则:")
    print("    → 直接使用localStorage的b1值")


def main():
    """主函数"""
    print("🎯 小红书 x-s-common 代码格式化和分析")
    print("=" * 60)
    print()
    
    # 格式化代码
    formatted = format_x_s_common()
    
    # 保存格式化后的代码
    with open("x-s-common-formatted.js", "w", encoding="utf-8") as f:
        f.write(formatted)
    
    print("✅ 格式化完成，已保存到: x-s-common-formatted.js")
    print()
    
    # 显示格式化后的代码
    print("📝 格式化后的代码:")
    print("-" * 60)
    print(formatted)
    print("-" * 60)
    print()
    
    # 分析算法
    analyze_algorithm()


if __name__ == "__main__":
    main()
