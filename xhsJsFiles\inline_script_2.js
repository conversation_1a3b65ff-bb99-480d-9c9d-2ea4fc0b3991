!function(e,r){try{var n="__FST__",t=["HTML","HEAD","META","LINK","SCRIPT","STYLE","NOSCRIPT"];e[n]=e[n]||{runned:!1,observer:null,mutaRecords:[],imgObserver:null,imgRecords:[],run:function(n){try{!n.runned&&(n.runned=!0,e.MutationObserver&&e.performance&&e.performance.now&&(n.observer=new e.MutationObserver((function(r){try{n.mutaRecords.push({mutations:r,startTime:e.performance.now()}),r.filter((function(e){var r=(e.target.nodeName||"").toUpperCase();return"childList"===e.type&&r&&-1===t.indexOf(r)&&e.addedNodes&&e.addedNodes.length})).forEach((function(r){[].slice.call(r.addedNodes,0).filter((function(e){var r=(e.nodeName||"").toUpperCase();return 1===e.nodeType&&"IMG"===r&&e.isConnected&&!e.closest("[fmp-ignore]")&&!e.hasAttribute("fmp-ignore")})).forEach((function(r){r.addEventListener("load",(function(){try{var t=e.performance.now(),o=r.getAttribute("src")||"";e.requestAnimationFrame((function i(a){try{r&&r.naturalWidth&&r.naturalHeight?n.imgRecords.push({name:o.split(":")[1]||o,responseEnd:a,loadTime:t,startTime:0,duration:0,type:"loaded"}):e.requestAnimationFrame(i)}catch(e){}}))}catch(e){}}))}))}))}catch(e){}})),n.observer.observe(r,{childList:!0,subtree:!0}),e.PerformanceObserver&&(n.imgObserver=new e.PerformanceObserver((function(e){try{e.getEntries().filter((function(e){return"img"===e.initiatorType||"css"===e.initiatorType||"link"===e.initiatorType})).forEach((function(e){n.imgRecords.push({name:e.name.split(":")[1]||e.name,responseEnd:e.responseEnd,startTime:e.startTime,duration:e.duration,type:"entry"})}))}catch(e){}})),n.imgObserver.observe({entryTypes:["resource"]}))))}catch(e){}}},e[n].runned||e[n].run(e[n])}catch(e){}}(window,document)