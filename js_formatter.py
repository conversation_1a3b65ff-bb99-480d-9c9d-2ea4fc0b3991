#!/usr/bin/env python3
"""
JavaScript代码格式化工具
将压缩的JS代码格式化为可读的形式
"""

import re
import argparse
from pathlib import Path


class JSFormatter:
    """JavaScript代码格式化器"""
    
    def __init__(self, indent_size=2):
        """
        初始化格式化器
        
        Args:
            indent_size: 缩进大小
        """
        self.indent_size = indent_size
        self.indent_level = 0
        
    def format_code(self, js_code):
        """
        格式化JavaScript代码
        
        Args:
            js_code: 压缩的JavaScript代码
            
        Returns:
            格式化后的代码
        """
        # 预处理：处理字符串和注释
        processed_code = self._preprocess_strings_and_comments(js_code)
        
        # 主要格式化逻辑
        formatted = self._format_main(processed_code)
        
        # 后处理：恢复字符串和注释
        result = self._postprocess_strings_and_comments(formatted)
        
        return result
    
    def _preprocess_strings_and_comments(self, code):
        """预处理字符串和注释，避免格式化时被破坏"""
        self.string_placeholders = {}
        self.comment_placeholders = {}
        
        # 保护字符串
        string_counter = 0
        
        # 处理双引号字符串
        def replace_double_quote_string(match):
            nonlocal string_counter
            placeholder = f"__STRING_DOUBLE_{string_counter}__"
            self.string_placeholders[placeholder] = match.group(0)
            string_counter += 1
            return placeholder
        
        # 处理单引号字符串
        def replace_single_quote_string(match):
            nonlocal string_counter
            placeholder = f"__STRING_SINGLE_{string_counter}__"
            self.string_placeholders[placeholder] = match.group(0)
            string_counter += 1
            return placeholder
        
        # 处理模板字符串
        def replace_template_string(match):
            nonlocal string_counter
            placeholder = f"__STRING_TEMPLATE_{string_counter}__"
            self.string_placeholders[placeholder] = match.group(0)
            string_counter += 1
            return placeholder
        
        # 替换字符串
        code = re.sub(r'"(?:[^"\\]|\\.)*"', replace_double_quote_string, code)
        code = re.sub(r"'(?:[^'\\]|\\.)*'", replace_single_quote_string, code)
        code = re.sub(r'`(?:[^`\\]|\\.)*`', replace_template_string, code)
        
        return code
    
    def _format_main(self, code):
        """主要格式化逻辑"""
        result = []
        i = 0
        
        while i < len(code):
            char = code[i]
            
            if char == '{':
                result.append(char)
                result.append('\n')
                self.indent_level += 1
                result.append(self._get_indent())
                
            elif char == '}':
                # 移除前面可能的空白和换行
                while result and result[-1] in [' ', '\t']:
                    result.pop()
                if result and result[-1] != '\n':
                    result.append('\n')
                
                self.indent_level -= 1
                result.append(self._get_indent())
                result.append(char)
                
                # 检查后面是否有其他内容
                if i + 1 < len(code) and code[i + 1] not in [';', ',', ')', ']', '}']:
                    result.append('\n')
                    result.append(self._get_indent())
                
            elif char == ';':
                result.append(char)
                # 检查是否在for循环中
                if not self._is_in_for_loop(code, i):
                    result.append('\n')
                    result.append(self._get_indent())
                else:
                    result.append(' ')
                
            elif char == ',':
                result.append(char)
                result.append(' ')
                
            elif char in ['(', '[']:
                result.append(char)
                # 检查是否需要换行（长参数列表）
                if self._should_break_after_paren(code, i):
                    result.append('\n')
                    self.indent_level += 1
                    result.append(self._get_indent())
                
            elif char in [')', ']']:
                # 移除前面可能的空白
                while result and result[-1] in [' ', '\t']:
                    result.pop()
                
                if self._should_break_before_paren(code, i):
                    if result and result[-1] != '\n':
                        result.append('\n')
                    self.indent_level -= 1
                    result.append(self._get_indent())
                
                result.append(char)
                
            elif char in ['+', '-', '*', '/', '=', '!', '<', '>', '&', '|']:
                # 处理操作符
                if self._should_add_space_around_operator(code, i):
                    if result and result[-1] != ' ':
                        result.append(' ')
                    result.append(char)
                    if i + 1 < len(code) and code[i + 1] != ' ':
                        result.append(' ')
                else:
                    result.append(char)
                
            elif char == '?':
                result.append(' ')
                result.append(char)
                result.append(' ')
                
            elif char == ':':
                result.append(char)
                result.append(' ')
                
            elif char in [' ', '\t', '\n', '\r']:
                # 跳过多余的空白字符
                if result and result[-1] not in [' ', '\t', '\n']:
                    result.append(' ')
                
            else:
                result.append(char)
            
            i += 1
        
        return ''.join(result)
    
    def _get_indent(self):
        """获取当前缩进"""
        return ' ' * (self.indent_level * self.indent_size)
    
    def _is_in_for_loop(self, code, pos):
        """检查分号是否在for循环中"""
        # 简单检查：向前查找最近的for关键字
        start = max(0, pos - 100)
        segment = code[start:pos]
        
        # 计算括号平衡
        paren_count = 0
        for char in reversed(segment):
            if char == ')':
                paren_count += 1
            elif char == '(':
                paren_count -= 1
                if paren_count < 0:
                    break
        
        return 'for(' in segment and paren_count >= 0
    
    def _should_break_after_paren(self, code, pos):
        """判断是否应该在括号后换行"""
        # 简单策略：如果括号内容很长就换行
        paren_count = 1
        length = 0
        i = pos + 1
        
        while i < len(code) and paren_count > 0:
            if code[i] == '(':
                paren_count += 1
            elif code[i] == ')':
                paren_count -= 1
            length += 1
            i += 1
            
            if length > 80:  # 超过80字符就换行
                return True
        
        return False
    
    def _should_break_before_paren(self, code, pos):
        """判断是否应该在括号前换行"""
        # 检查前面是否有换行的迹象
        return False  # 简化处理
    
    def _should_add_space_around_operator(self, code, pos):
        """判断操作符周围是否应该加空格"""
        char = code[pos]
        
        # 特殊情况处理
        if char in ['!', '+', '-']:
            # 检查是否是一元操作符
            if pos == 0:
                return False
            prev_char = code[pos - 1]
            if prev_char in ['(', '[', '{', '=', '!', '<', '>', '&', '|', '?', ':', ',', ';']:
                return False
        
        return True
    
    def _postprocess_strings_and_comments(self, code):
        """恢复字符串和注释"""
        # 恢复字符串
        for placeholder, original in self.string_placeholders.items():
            code = code.replace(placeholder, original)
        
        # 恢复注释
        for placeholder, original in self.comment_placeholders.items():
            code = code.replace(placeholder, original)
        
        return code
    
    def format_function_calls(self, code):
        """特别处理函数调用的格式化"""
        # 处理function关键字
        code = re.sub(r'\bfunction\s*\(', 'function (', code)
        
        # 处理常见的JavaScript关键字
        keywords = ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'try', 'catch', 'finally', 'return', 'var', 'let', 'const']
        for keyword in keywords:
            code = re.sub(rf'\b{keyword}\s*\(', f'{keyword} (', code)
        
        return code
    
    def clean_up_formatting(self, code):
        """清理格式化结果"""
        # 移除多余的空行
        code = re.sub(r'\n\s*\n\s*\n', '\n\n', code)
        
        # 修复一些常见的格式问题
        code = re.sub(r'{\s*\n\s*}', '{}', code)  # 空对象
        code = re.sub(r'\[\s*\n\s*\]', '[]', code)  # 空数组
        
        return code.strip()


def format_js_file(input_file, output_file=None, indent_size=2):
    """
    格式化JavaScript文件
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径（可选）
        indent_size: 缩进大小
    """
    formatter = JSFormatter(indent_size)
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        js_code = f.read()
    
    # 格式化代码
    formatted_code = formatter.format_code(js_code)
    formatted_code = formatter.format_function_calls(formatted_code)
    formatted_code = formatter.clean_up_formatting(formatted_code)
    
    # 输出结果
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(formatted_code)
        print(f"格式化完成，结果保存到: {output_file}")
    else:
        print("格式化结果:")
        print("-" * 50)
        print(formatted_code)
        print("-" * 50)
    
    return formatted_code


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='JavaScript代码格式化工具')
    parser.add_argument('input', help='输入的JavaScript文件或代码')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-i', '--indent', type=int, default=2, help='缩进大小 (默认: 2)')
    parser.add_argument('-c', '--code', action='store_true', help='直接格式化提供的代码字符串')
    
    args = parser.parse_args()
    
    formatter = JSFormatter(args.indent)
    
    if args.code:
        # 直接格式化代码字符串
        formatted = formatter.format_code(args.input)
        formatted = formatter.format_function_calls(formatted)
        formatted = formatter.clean_up_formatting(formatted)
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(formatted)
            print(f"格式化完成，结果保存到: {args.output}")
        else:
            print("格式化结果:")
            print("-" * 50)
            print(formatted)
            print("-" * 50)
    else:
        # 格式化文件
        format_js_file(args.input, args.output, args.indent)


if __name__ == "__main__":
    # 如果没有命令行参数，格式化x-s-common.js文件
    import sys
    if len(sys.argv) == 1:
        if Path("x-s-common.js").exists():
            print("格式化 x-s-common.js 文件...")
            format_js_file("x-s-common.js", "x-s-common-formatted.js")
        else:
            print("请提供JavaScript文件路径或使用 -h 查看帮助")
    else:
        main()
