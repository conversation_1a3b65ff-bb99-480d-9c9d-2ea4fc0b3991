{"xsCommon_function": {"simple_body": "function xsCommon(e,r){var i,a;try{var s=e.platform,u=r.url;if(S.map(function(e){return new RegExp(e)}", "complete_body": "{var i,a;try{var s=e.platform,u=r.url;if(S.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)}),!utils_shouldSign(u))return r;var c=r.headers[\"X-Sign\"]||\"\",d=getSigCount(c),p=localStorage.getItem(\"b1\"),f=localStorage.getItem(\"b1b1\")||\"1\",v={s0:getPlatformCode(s),s1:\"\",x0:f,x1:C,x2:s||\"PC\",x3:\"xhs-pc-web\",x4:\"4.68.0\",x5:l.Z.get(\"a1\"),x6:\"\",x7:\"\",x8:p,x9:O(\"\".concat(\"\").concat(\"\").concat(p)),x10:d,x11:\"normal\"},h=k.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)});(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O(\"\".concat(\"\").concat(\"\").concat(e)),r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}", "start_pos": 200604, "end_pos": 200706}, "dependency_functions": {"getPlatformCode": [{"pattern_matched": "function\\s+getPlatformCode\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "function getPlatformCode(e){switch(e){case\"Android\":return s.Android;case\"iOS\":return s.iOS;case\"Mac OS\":return s.MacOs;case\"Linux\":return s.Linux;default:return s.other}", "complete_body": "{switch(e){case\"Android\":return s.Android;case\"iOS\":return s.iOS;case\"Mac OS\":return s.MacOs;case\"Linux\":return s.Linux;default:return s.other}}", "start_pos": 179769}], "getSigCount": [{"pattern_matched": "function\\s+getSigCount\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "function getSigCount(e){var r=Number(sessionStorage.getItem(\"sc\"))||0;return e&&(r++,sessionStorage.setItem(\"sc\",r.toString())),r}", "complete_body": "{var r=Number(sessionStorage.getItem(\"sc\"))||0;return e&&(r++,sessionStorage.setItem(\"sc\",r.toString())),r}", "start_pos": 201490}], "utils_shouldSign": [{"pattern_matched": "function\\s+utils_shouldSign\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "function utils_shouldSign(e){var r=!0;return e.indexOf(window.location.host)>-1||e.indexOf(\"sit.xiaohongshu.com\")>-1?r:(g.some(function(i){if(e.indexOf(i)>-1)return r=!1,!0}", "complete_body": "{var r=!0;return e.indexOf(window.location.host)>-1||e.indexOf(\"sit.xiaohongshu.com\")>-1?r:(g.some(function(i){if(e.indexOf(i)>-1)return r=!1,!0}),r)}", "start_pos": 180775}], "b64Encode": [{"pattern_matched": "function\\s+b64Encode\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "function b64Encode(e){for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)s.push(encodeChunk(e,u,u+16383>c?c:u+16383));return 1===a?(r=e[i-1],s.push(P[r>>2]+P[r<<4&63]+\"==\")):2===a&&(r=(e[i-2]<<8)+e[i-1],s.push(P[r>>10]+P[r>>4&63]+P[r<<2&63]+\"=\")),s.join(\"\")}", "complete_body": "{for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)s.push(encodeChunk(e,u,u+16383>c?c:u+16383));return 1===a?(r=e[i-1],s.push(P[r>>2]+P[r<<4&63]+\"==\")):2===a&&(r=(e[i-2]<<8)+e[i-1],s.push(P[r>>10]+P[r>>4&63]+P[r<<2&63]+\"=\")),s.join(\"\")}", "start_pos": 169719}], "encodeUtf8": [{"pattern_matched": "function\\s+encodeUtf8\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "function encodeUtf8(e){for(var r=encodeURIComponent(e),i=[],a=0;a<r.length;a++){var s=r.charAt(a);if(\"%\"===s){var u=parseInt(r.charAt(a+1)+r.charAt(a+2),16);i.push(u),a+=2}", "complete_body": "{for(var r=encodeURIComponent(e),i=[],a=0;a<r.length;a++){var s=r.charAt(a);if(\"%\"===s){var u=parseInt(r.charAt(a+1)+r.charAt(a+2),16);i.push(u),a+=2}else i.push(s.charCodeAt(0))}return i}", "start_pos": 169509}], "O": [{"pattern_matched": "function\\s+O\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "function o(e){return\"function\"==typeof e.readFloatLE&&\"function\"==typeof e.slice&&t(e.slice(0,0))}", "complete_body": "{return\"function\"==typeof e.readFloatLE&&\"function\"==typeof e.slice&&t(e.slice(0,0))}", "start_pos": 176127}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o:function(){return postApiSnsWebV1LoginSocial}", "complete_body": "{return postApiSnsWebV1LoginSocial}", "start_pos": 13267}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(){var e;if(null===(e=window)||void 0===e?void 0:e.__baseInfo__)try{var r=JSON.parse(window.__baseInfo__);D.value=Object.keys(r).length?r:void 0}", "complete_body": "{var e;if(null===(e=window)||void 0===e?void 0:e.__baseInfo__)try{var r=JSON.parse(window.__baseInfo__);D.value=Object.keys(r).length?r:void 0}catch(e){D.value=void 0}else D.value=void 0}", "start_pos": 19244}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(e){var r=e.userId,i=e.userToken,a=e.sessionId,s=e.hashExp;return r&&(Z.userId=r),i&&(Z.userToken=i),a&&(Z.sessionId=a),s&&\"string\"==typeof s&&(Z.hashExp=s),Z}", "complete_body": "{var r=e.userId,i=e.userToken,a=e.sessionId,s=e.hashExp;return r&&(Z.userId=r),i&&(Z.userToken=i),a&&(Z.sessionId=a),s&&\"string\"==typeof s&&(Z.hashExp=s),Z}", "start_pos": 28594}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(e){if(!isBrowser())return{}", "complete_body": "{if(!isBrowser())return{};var r=e?getPath(e):parseUrl(window.location.href);return $.setSessionId(r),{context_matchedPath:r,context_route:window.location.href,context_userAgent:window.navigator.userAgent}}", "start_pos": 30508}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}", "complete_body": "{arguments.length>0&&void 0!==arguments[0]&&arguments[0];var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.getUserInfo;return r?new Promise(function(e){setTimeout(function(){r().then(function(r){e(r)}).catch(function(){e(ee)})})}):getUserInfoPromise()}", "start_pos": 30779}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}", "complete_body": "{var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return et=getUserInfo(e,r)}", "start_pos": 31099}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}", "complete_body": "{var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r={artifactName:e.package&&e.package.name||\"xhs-pc-web\",artifactVersion:e.package&&e.package.version||\"4.68.0\"};return e.getArtifactInfo?(0,g._)({},r,e.getArtifactInfo()):r}", "start_pos": 31278}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(){var e,r;return{cpuCores:null===(e=window.navigator)||void 0===e?void 0:e.hardwareConcurrency,deviceMemory:null===(r=window.navigator)||void 0===r?void 0:r.deviceMemory}", "complete_body": "{var e,r;return{cpuCores:null===(e=window.navigator)||void 0===e?void 0:e.hardwareConcurrency,deviceMemory:null===(r=window.navigator)||void 0===r?void 0:r.deviceMemory}}", "start_pos": 110966}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "O=function(e){for(var r,i,a=256,s=[];a--;s[a]=r>>>0)for(i=8,r=a;i--;)r=1&r?r>>>1^0xedb88320:r>>>1;return function(e){if(\"string\"==typeof e){for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e.charCodeAt(r)]^i>>>8;return -1^i^0xedb88320}", "complete_body": "{for(var r,i,a=256,s=[];a--;s[a]=r>>>0)for(i=8,r=a;i--;)r=1&r?r>>>1^0xedb88320:r>>>1;return function(e){if(\"string\"==typeof e){for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e.charCodeAt(r)]^i>>>8;return -1^i^0xedb88320}for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e[r]]^i>>>8;return -1^i^0xedb88320}}", "start_pos": 169985}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)}", "complete_body": "{return Object.prototype.hasOwnProperty.call(e,r)}", "start_pos": 170707}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(e,r){var i,a,s,u,c,l,d,p,f,v,h,g,m,_,y,w,E,T=\"\",S=\"\",b=0,k=0,C=0,P=0,A=0,R=0;if((null===(a=e.event)||void 0===a?void 0:null===(i=a.value)||void 0===i?void 0:i.pointId)&&(b=e.event.value.pointId),null===(c=e.page)||void 0===c?void 0:null===(u=c.value)||void 0===u?void 0:null===(s=u.pageInstance)||void 0===s?void 0:s.value){T=e.page.value.pageInstance.value;var I=e.page.value.pageInstance.value.toUpperCase();k=(null==r?void 0:r.PageInstance[\"\".concat(I)])||0}", "complete_body": "{var i,a,s,u,c,l,d,p,f,v,h,g,m,_,y,w,E,T=\"\",S=\"\",b=0,k=0,C=0,P=0,A=0,R=0;if((null===(a=e.event)||void 0===a?void 0:null===(i=a.value)||void 0===i?void 0:i.pointId)&&(b=e.event.value.pointId),null===(c=e.page)||void 0===c?void 0:null===(u=c.value)||void 0===u?void 0:null===(s=u.pageInstance)||void 0===s?void 0:s.value){T=e.page.value.pageInstance.value;var I=e.page.value.pageInstance.value.toUpperCase();k=(null==r?void 0:r.PageInstance[\"\".concat(I)])||0}if(null===(p=e.event)||void 0===p?void 0:null===(d=p.value)||void 0===d?void 0:null===(l=d.action)||void 0===l?void 0:l.value){var O=e.event.value.action.value.toUpperCase();C=(null==r?void 0:r.NormalizedAction[\"\".concat(O)])||0}if(null===(h=e.event)||void 0===h?void 0:null===(v=h.value)||void 0===v?void 0:null===(f=v.actionInteractionType)||void 0===f?void 0:f.value){var N=e.event.value.actionInteractionType.value.toUpperCase();P=(null==r?void 0:r.ActionInteractionType[\"\".concat(N)])||0}if(null===(_=e.event)||void 0===_?void 0:null===(m=_.value)||void 0===m?void 0:null===(g=m.targetType)||void 0===g?void 0:g.value){var L=e.event.value.targetType.value.toUpperCase();A=(null==r?void 0:r.RichTargetType[\"\".concat(L)])||0}if(null===(E=e.event)||void 0===E?void 0:null===(w=E.value)||void 0===w?void 0:null===(y=w.targetDisplayType)||void 0===y?void 0:y.value){var M=e.event.value.targetDisplayType.value.toUpperCase();R=(null==r?void 0:r.TargetDisplayType[\"\".concat(M)])||0}return S=\"\".concat(k,\"^\").concat(C,\"^\").concat(P,\"^\").concat(A,\"^\").concat(R),{pointId:b,pageInstanceStr:T,pageInstance:k,action:C,actionInteractionType:P,targetType:A,targetDisplayType:R,referKey:S}}", "start_pos": 258940}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(){return getABInfo().then(function(e){return{user:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}", "complete_body": "{return getABInfo().then(function(e){return{user:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}}}})}", "start_pos": 306309}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}", "complete_body": "{var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=i.getUserInfo?i.getUserInfo():r.purgeUser?purgeUserInfo():meta_user(),s={artifactName:\"xhs-pc-web\",artifactVersion:\"4.68.0\"},u=i.getArtifactInfo?(0,v._)({},s,i.getArtifactInfo()):s;return Promise.all([(e=i,new Promise(function(r){y.YF.isXHS?(0,eu.dw)(\"getDeviceInfo\").then(function(e){e.value?r(deviceBuilderV2(e.value)):r(deviceBuilderV2())}).catch(function(){r(deviceBuilderV2())}):e.getDeviceInfo?e.getDeviceInfo().then(function(e){r(deviceBuilderV2({},e))}).catch(function(){r(deviceBuilderV2())}):r(deviceBuilderV2())})),eQ,a,getBrowserInfoV2(r.route),u]).then(function(e){var r=(0,j._)(e,5),i=r[0],a=r[1],s=r[2],u=r[3],c=r[4],l=(0,v._)({},i,a,u);return l.context_artifactName=c.artifactName,l.context_artifactVersion=c.artifactVersion,(null==s?void 0:s.user)&&(l.context_userId=s.user.value.userId),l})}", "start_pos": 307679}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(e){return data_meta(e,w).then(function(e){e5.extend(e,eN.NAME),e5.extend(e,eM.NAME),e5.extend(e,eL.NAME),r()}", "complete_body": "{return data_meta(e,w).then(function(e){e5.extend(e,eN.NAME),e5.extend(e,eM.NAME),e5.extend(e,eL.NAME),r()})}", "start_pos": 316041}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(e){return getBaseInfo(e,w).then(function(e){e9[ex.NAME]=e,r()}", "complete_body": "{return getBaseInfo(e,w).then(function(e){e9[ex.NAME]=e,r()})}", "start_pos": 316726}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o:function(e){setUserId(e9,e)}", "complete_body": "{setUserId(e9,e)}", "start_pos": 322939}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o:function(){return h}", "complete_body": "{return h}", "start_pos": 347577}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o:function(e){return webp2png(e),e}", "complete_body": "{return webp2png(e),e}", "start_pos": 376469}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o:function(){return prado_invoke_invoke}", "complete_body": "{return prado_invoke_invoke}", "start_pos": 412863}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o:function(){return prado_invoke_subscribe}", "complete_body": "{return prado_invoke_subscribe}", "start_pos": 413114}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o:function(e){return webp2png(e),e}", "complete_body": "{return webp2png(e),e}", "start_pos": 428971}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o:function(){return validateRes}", "complete_body": "{return validateRes}", "start_pos": 459716}, {"pattern_matched": "O\\s*[:=]\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "o=function(){var e,r,i,a,s,u,c=null!==(r=null===(e=navigator)||void 0===e?void 0:e.userAgent)&&void 0!==r?r:\"\";return c.indexOf(\"Opera\")>-1||c.indexOf(\"OPR\")>-1?(i=\"Opera\",a=(a=c.match(/(Opera|OPR)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):c.indexOf(\"Edg\")>-1?(i=\"Microsoft Edge\",a=(a=c.match(/(Edg)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):c.indexOf(\"Chrome\")>-1?(i=\"Chrome\",a=(a=c.match(/(Chrome)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):c.indexOf(\"Safari\")>-1?(i=\"Safari\",a=(a=c.match(/(Safari)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):c.indexOf(\"Firefox\")>-1?(i=\"Firefox\",a=(a=c.match(/(Firefox)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):(i=\"Unknown\",a=\"0.0.0\"),-1!==c.indexOf(\"Windows\")?(s=\"Windows\",u=(u=c.match(/Windows NT\\s*(\\d+\\.\\d+)/))?u[1]:\"Unknown\"):-1!==c.indexOf(\"Mac OS X\")?(s=\"macOS\",u=(u=c.match(/Mac OS X\\s*(\\d+[_.]\\d+)/))?u[1].replace(/_/g,\".\"):\"Unknown\"):-1!==c.indexOf(\"Android\")?(s=\"Android\",u=(u=c.match(/Android\\s*(\\d+\\.\\d+)/))?u[1]:\"Unknown\"):(-1!==c.indexOf(\"Linux\")?s=\"Linux\":s=\"Unknown\",u=\"Unknown\"),{browserName:i,browserVersion:a,osName:s,osVersion:u,userAgent:c}", "complete_body": "{var e,r,i,a,s,u,c=null!==(r=null===(e=navigator)||void 0===e?void 0:e.userAgent)&&void 0!==r?r:\"\";return c.indexOf(\"Opera\")>-1||c.indexOf(\"OPR\")>-1?(i=\"Opera\",a=(a=c.match(/(Opera|OPR)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):c.indexOf(\"Edg\")>-1?(i=\"Microsoft Edge\",a=(a=c.match(/(Edg)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):c.indexOf(\"Chrome\")>-1?(i=\"Chrome\",a=(a=c.match(/(Chrome)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):c.indexOf(\"Safari\")>-1?(i=\"Safari\",a=(a=c.match(/(Safari)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):c.indexOf(\"Firefox\")>-1?(i=\"Firefox\",a=(a=c.match(/(Firefox)\\/?\\s*(\\.?\\d+(\\.\\d+)*)/i))?a[2]:\"0.0.0\"):(i=\"Unknown\",a=\"0.0.0\"),-1!==c.indexOf(\"Windows\")?(s=\"Windows\",u=(u=c.match(/Windows NT\\s*(\\d+\\.\\d+)/))?u[1]:\"Unknown\"):-1!==c.indexOf(\"Mac OS X\")?(s=\"macOS\",u=(u=c.match(/Mac OS X\\s*(\\d+[_.]\\d+)/))?u[1].replace(/_/g,\".\"):\"Unknown\"):-1!==c.indexOf(\"Android\")?(s=\"Android\",u=(u=c.match(/Android\\s*(\\d+\\.\\d+)/))?u[1]:\"Unknown\"):(-1!==c.indexOf(\"Linux\")?s=\"Linux\":s=\"Unknown\",u=\"Unknown\"),{browserName:i,browserVersion:a,osName:s,osVersion:u,userAgent:c}}", "start_pos": 536043}, {"pattern_matched": "var\\s+O\\s*=\\s*function\\s*\\([^)]*\\)\\s*\\{[^}]*\\}", "simple_body": "var O=function(e){for(var r,i,a=256,s=[];a--;s[a]=r>>>0)for(i=8,r=a;i--;)r=1&r?r>>>1^0xedb88320:r>>>1;return function(e){if(\"string\"==typeof e){for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e.charCodeAt(r)]^i>>>8;return -1^i^0xedb88320}", "complete_body": "{for(var r,i,a=256,s=[];a--;s[a]=r>>>0)for(i=8,r=a;i--;)r=1&r?r>>>1^0xedb88320:r>>>1;return function(e){if(\"string\"==typeof e){for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e.charCodeAt(r)]^i>>>8;return -1^i^0xedb88320}for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e[r]]^i>>>8;return -1^i^0xedb88320}}", "start_pos": 169981}]}, "constants": {"E": {"value": "c.sent(),u&&u(E),[2,{result:E,retryTimes:s,retryErrors:(0,r.formatErrors)(l),transportType:w}]", "position": 546262}, "K": {"value": "i(63522)", "position": 417329}, "Z": {"value": "((a={}).Authorized=\"authorized\",a.Denied=\"denied\",a.Restricted=\"restricted\",a.Undetermined=\"undetermined\",a),Q=i(3511)", "position": 417344}, "Q": {"value": "{toast:showToast,showToast:showToast,showalertV2:showAlert,showAlert:showAlert,removeVCFromStack:removeVCFromStackIOS,removeVCFromStackIOS:removeVCFromStackIOS,showNavigationRightBarButtonItemV2:showNavigationRightBarButtonItem,showNavigationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePayIOS:supportApplePayIOS,applePayClient:applePayClientIOS,applePayClientIOS:applePayClientIOS,replaceSelfWithLink:replaceSelfWithLink,replaceSelfWithLinkV2:replaceSelfWithLinkV2Android,replaceSelfWithLinkV2Android:replaceSelfWithLinkV2Android,replaceRouteWith:replaceRouteWith,showActionSheet:showActionSheet,setNavigationHidden:setNavigationHidden,setStatusBarHiddenIOS:setStatusBarHiddenIOS,setStatusBarStyleIOS:setStatusBarTextColor,setStatusBarTextColor:setStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat:openURLByWechat,wechatPayClient:wechatPayClient,openLink:openLinkAndroid,closeWindow:closeWindow,openGiftPanel:openGiftPanel,setPasteBoard:setPasteBoard,showTrack:showTrack,showApmTrack:showApmTrack,webtrack:webtrack,emitTrack:emitTrack,emitApmTrack:emitApmTrack,openMapWithLocation:openMapWithLocation,confirmAntiSpam:confirmAntiSpam,addComment:addComment,openXhsSystemSettings:openXhsSystemSettings,openRechargeCoinPanel:openRechargeCoinPanel,openFansPanel:openFansPanel,getMessageStatusIOS:getMessageStatusIOS,getMessageStatus:getMessageStatusIOS,areNotificationsEnabledAndroid:areNotificationsEnabledAndroid,areNotificationsEnabled:areNotificationsEnabledAndroid,getFileUrlFromLocalServerIOS:getFileUrlFromLocalServerIOS,getFileUrlFromLocalServer:getFileUrlFromLocalServerIOS,checkLoginWithAction:checkLoginWithAction,logout:logout,isAppInstalled:isAppInstalled,getAppInfo:getAppInfo,getDeviceInfo:getDeviceInfoOld,getNetworkType:getNetworkTypeLegacy,getUserInfo:getUserInfo,getTrackEnv:getTrackEnv,lowPowerModeEnabled:lowPowerModeEnabled,requestNotificationPermission:requestNotificationPermission,saveImage:saveImage,basicSendClientRequest:basicSendClientRequest,sendClientRequest:sendClientRequest,sendClientRequestV2:sendClientRequestV2,getPrevData:getPrevData,getItem:getItem,setItem:setItem,removeItem:removeItem,broadcast:broadcast,broadcastNative:broadcastNative,getThirdAuth:getThirdAuth,getCurrentGeolocation:getCurrentGeolocation,checkAppPermission:checkAppPermission,toggleLocalDns:toggleLocalDns,registerTrickleConnectTopic:registerTrickleConnectTopic,getABFlag:getABFlag},e$={xhs:{},top:{}},e0={xhs:{},top:{}}", "position": 399700}, "T": {"value": "c.sent(),l.push(T),!i.useRetry)throw new f.CustomAggregateError(l,\"no retry option\",0)", "position": 546369}, "C": {"value": "this", "position": 520469}, "P": {"value": "i(70076),A=i(41622),R=i(377),I=i(7862),O=i.n(I),N=i(36757)", "position": 429430}, "S": {"value": "b.stop,b.newPromise&&(g=b.newPromise)):S=b,c.label=5", "position": 546537}, "R": {"value": "b.shift()", "position": 455456}, "O": {"value": "browser_getBrowserVersion(p)", "position": 369593}, "A": {"value": "A.then(wrapFulfilled(null==R?void 0:R.fulfilled),wrapRejected(null==R?void 0:R.rejected))}return A}function isSupportInvoke(){var e,r,i,a=s.YF.isIOS?null===(r=window)||void 0===r?void 0:null===(e=r.webkit)||void 0===e?void 0:e.messageHandlers:null===(i=window)||void 0===i?void 0:i.XHSBridge", "position": 455468}, "N": {"value": "{}).Image=\"image\",<PERSON>.<PERSON>=\"link\",N.Text=\"text\",N)", "position": 426116}, "B": {"value": "i(31547)", "position": 431942}, "L": {"value": "O().oneOf([0,-1]).isRequired", "position": 429504}, "F": {"value": "{toast:showToast,showToast:showToast,showalertV2:showAlert,showAlert:showAlert,removeVCFromStack:removeVCFromStackIOS,removeVCFromStackIOS:removeVCFromStackIOS,showNavigationRightBarButtonItemV2:showNavigationRightBarButtonItem,showNavigationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePayIOS:supportApplePayIOS,applePayClient:applePayClientIOS,applePayClientIOS:applePayClientIOS,replaceSelfWithLink:replaceSelfWithLink,replaceSelfWithLinkV2:replaceSelfWithLinkV2Android,replaceSelfWithLinkV2Android:replaceSelfWithLinkV2Android,replaceRouteWith:replaceRouteWith,showActionSheet:showActionSheet,setNavigationHidden:setNavigationHidden,setStatusBarHiddenIOS:setStatusBarHiddenIOS,setStatusBarStyleIOS:setStatusBarTextColor,setStatusBarTextColor:setStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat:openURLByWechat,wechatPayClient:wechatPayClient,openLink:openLinkAndroid,closeWindow:closeWindow,openGiftPanel:openGiftPanel,setPasteBoard:setPasteBoard,showTrack:showTrack,showApmTrack:showApmTrack,webtrack:webtrack,emitTrack:emitTrack,emitApmTrack:emitApmTrack,openMapWithLocation:openMapWithLocation,confirmAntiSpam:confirmAntiSpam,addComment:addComment,openXhsSystemSettings:openXhsSystemSettings,openRechargeCoinPanel:openRechargeCoinPanel,openFansPanel:openFansPanel,getMessageStatusIOS:getMessageStatusIOS,getMessageStatus:getMessageStatusIOS,areNotificationsEnabledAndroid:areNotificationsEnabledAndroid,areNotificationsEnabled:areNotificationsEnabledAndroid,getFileUrlFromLocalServerIOS:getFileUrlFromLocalServerIOS,getFileUrlFromLocalServer:getFileUrlFromLocalServerIOS,checkLoginWithAction:checkLoginWithAction,logout:logout,isAppInstalled:isAppInstalled,getAppInfo:getAppInfo,getDeviceInfo:getDeviceInfoOld,getNetworkType:getNetworkTypeLegacy,getUserInfo:getUserInfo,getTrackEnv:getTrackEnv,lowPowerModeEnabled:lowPowerModeEnabled,requestNotificationPermission:requestNotificationPermission,saveImage:saveImage,basicSendClientRequest:basicSendClientRequest,sendClientRequest:sendClientRequest,sendClientRequestV2:sendClientRequestV2,getPrevData:getPrevData,getItem:getItem,setItem:setItem,removeItem:removeItem,broadcast:broadcast,broadcastNative:broadcastNative,getThirdAuth:getThirdAuth,getCurrentGeolocation:getCurrentGeolocation,checkAppPermission:checkAppPermission,toggleLocalDns:toggleLocalDns,registerTrickleConnectTopic:registerTrickleConnectTopic,getABFlag:getABFlag},V={xhs:{},top:{}},H={xhs:{},top:{}}", "position": 451152}, "U": {"value": "{})).Back=\"back\",a.<PERSON>=\"default\",a.Foreground=\"foreground\",(s=q||(q={})).Authorized=\"authorized\",s.Denied=\"denied\",s.Restricted=\"restricted\",s.Undetermined=\"undetermined\",(u=j||(j={})).Denied=\"denied\",u.Granted=\"granted\",u.Undetermined=\"undetermined\",(c=W||(W={})).Authorized=\"authorized\",c.Denied=\"denied\",(l=G||(G={})).EditProfileChangedData=\"edit_profile_changed_data\",l.EditProfilePreviewData=\"edit_profile_preview_data\",(d=J||(J={})).Android=\"Android\",d.<PERSON>=\"harmony\",d.IOS=\"iOS\",(X||(X={})).EditProfileChangedData=\"edit_profile_changed_data\",(p=Y||(Y={})).DOMInsert=\"domInsert\",p.<PERSON>=\"destroy\",p.Play=\"play\",p.SetPlayer=\"setPlayer\",p.Stop=\"stop\",(f=z||(z={})).The1=\"1\",f.The2=\"2\",f.The3=\"3\",(v=K||(K={})).Multi=\"multi\",v.Normal=\"normal\",v.Video=\"video\",(h=Z||(Z={})).CnyTopicFeed=\"cny_topic_feed\",h.Topic=\"topic\",h.TopicDiscussion=\"topic_discussion\",h.TopicRating=\"topic_rating\",(g=Q||(Q={})).Normal=\"normal\",g.Video=\"video\",($||($={})).Mp4=\"mp4\",(m=ee||(ee={})).EditProfileChangedData=\"edit_profile_changed_data\",m.EditProfilePreviewData=\"edit_profile_preview_data\",(_=et||(et={})).EditProfileChangedData=\"edit_profile_changed_data\",_.EditProfilePreviewData=\"edit_profile_preview_data\",(y=er||(er={})).ApplicationJSON=\"application/json\",y.ApplicationXWWWFormUrlencoded=\"application/x-www-form-urlencoded\",y.ImagePNG=\"image/png\",(w=en||(en={})).Delete=\"DELETE\",w.Get=\"GET\",w.Post=\"POST\",w.Put=\"PUT\",(E=ei||(ei={})).AccountDidSwitch=\"accountDidSwitch\",E.BrandAreaCard=\"brand-area-card\",E.BusinessBindsUpdate=\"business_binds_update\",E.CapaActivityTopicResult=\"capaActivityTopicResult\",E.CapaAdvanceOptionLiveTrailerConfigUpdate=\"capa_advance_option_live_trailer_config_update\",E.CheckoutPostOrder=\"checkout_post_order\",E.CloseLocationPage=\"closeLocationPage\",E.ClosePoiCard=\"closePoiCard\",E.CnyFMPFinished=\"cnyFMPFinished\",E.CoinRechargeFinishEvent=\"coin_recharge_finish_event\",E.CommonAddressRn=\"common-address-rn\",E.CurationKListRefreshCategory=\"curation_k_list_refresh_category\",E.CurationKMoveCategory=\"curation_k_move_category\",E.CustomerServiceQuickOrderCreate=\"customer_service_quick_order_create\",E.CyberidentityAuthResult=\"cyberidentityAuthResult\",E.DeclarePrivacyChange=\"declare_privacy_change\",E.DspyxisDetailUpdateCompass=\"dspyxis-detailUpdate-compass\",E.EoiMediaOffLine=\"eoi_media_off_line\",E.ExitPoipage=\"exitPoipage\",E.FinanceRechargeH5=\"finance-recharge-h5\",E.FloatingClose=\"floating_close\",E.FloatingFull=\"floating_full\",E.ForceUpdateCnyTab=\"forceUpdateCnyTab\",E.GameplayWishSuccess=\"gameplayWishSuccess\",E.GetLiveFloatingStatus=\"getLiveFloatingStatus\",E.GoCapaByCloseWindow=\"goCapaByCloseWindow\",E.GroupTopicSelectGoods=\"group_topic_select_goods\",E.GrowthFeeds=\"GrowthFeeds\",E.InspirationCollectBroadcast=\"inspiration_collect_broadcast\",E.IosLiveMerchantNotSupportTopUpAlert=\"ios_live_merchant_not_support_top_up_alert\",E.KeyboardResponded=\"keyboard_responded\",E.Kuri=\"kuri\",E.LancerAddressGrowth=\"lancer-address-growth\",E.LancerAddressMp=\"lancer-address-mp\",E.LancerAddressSlim=\"lancer-address-slim\",E.LancerInvoiceSlim=\"lancer-invoice-slim\",E.LancerSparkSlim=\"lancer-spark-slim\",E.LegoBoxCrossPlatformEvent=\"legoBoxCrossPlatformEvent\",E.LiveDynamicBridge=\"live_dynamic_bridge\",E.LiveFloatWindowMute=\"live_float_window_mute\",E.LiveRefreshCouponList=\"live_refresh_coupon_list\",E.LiveRoomBridge=\"live-room-bridge\",E.MarketingBoxClose=\"marketingBoxClose\",E.MeituanNotification=\"MeituanNotification\",E.MemoryWarningNotification=\"memoryWarningNotification\",E.MoreClassify=\"more_classify\",E.NativePostOrder=\"native_post_order\",E.NativeRnNoteLike=\"native-rn-note-like\",E.ObtainedGoldenNickname=\"obtained_golden_nickname\",E.OpenScreenshot=\"openScreenshot\",E.OpenSnackbar=\"openSnackbar\",E.OrderCreated=\"order-created\",E.PoiCommentPopoverImpression=\"poiCommentPopoverImpression\",E.PoiCommentTagClick=\"poiCommentTagClick\",E.PoiCommentTagImpression=\"poiCommentTagImpression\",E.PoiDeclareOnUpdate=\"poi_declare_on_update\",E.PoiDetailOnShow=\"poiDetailOnShow\",E.PoiPopcornDayRankExit=\"poi-popcorn-day-rank-exit\",E.PoiPopcornDetailPkExit=\"poi-popcorn-detail-pk-exit\",E.PoiPopcornTotalRankExit=\"poi-popcorn-total-rank-exit\",E.PostNoteGoodsResult=\"postNoteGoodsResult\",E.ProfileRefreshKey=\"ProfileRefreshKey\",E.PurchaseRefresh=\"purchase_refresh\",E.RealNameVerifyCompleted=\"realNameVerifyCompleted\",E.RedCAPTCHAVerifySuccess=\"red_captcha_verify_success\",E.RelieveFreezeAccountSuccess=\"relieve_freeze_account_success\",E.ReportFinishEvent=\"report_finish_event\",E.ReportNoteRemoveEvent=\"report_note_remove_event\",E.RnDevtools=\"rn-devtools\",E.RnDevtoolsEmitter=\"rn-devtools-emitter\",E.RnNativeNoteLike=\"rn-native-note-like\",E.RnPostOrder=\"rn_post_order\",E.SNSRNDiscoverCommentAction=\"SNSRNDiscoverCommentAction\",E.SearchRNCardImpressionEvent=\"searchRNCardImpressionEvent\",E.SearchRnCard=\"search_rn_card\",E.SellerFeedbackAction=\"seller-feedback-action\",E.SetProfileEntries=\"setProfileEntries\",E.ShareLiveTrailerCard=\"share_live_trailer_card\",E.ShowTopicReadTask=\"show_topic_read_task\",E.SizeBeenEntered=\"size-been-entered\",E.SqaLike=\"sqa-like\",E.SyncScrollviewRef=\"syncScrollviewRef\",E.Test=\"test\",E.TranslateY=\"translateY\",E.UpdateRnLiveNoticeListInfo=\"update_rn_live_notice_list_info\",E.UpdateTravelInfo=\"updateTravelInfo\",E.VisitedChanged=\"visitedChanged\",(T=eo||(eo={})).BusinessExecutionEnd=\"businessExecutionEnd\",T.BusinessExecutionStart=\"businessExecutionStart\",T.CoreHTTPRequestEnd=\"coreHttpRequestEnd\",T.CoreHTTPRequestStart=\"coreHttpRequestStart\",T.FirstMeaningfulPaint=\"firstMeaningfulPaint\",T.FrameExecutionEnd=\"frameExecutionEnd\",T.FrameExecutionStart=\"frameExecutionStart\",T.RouterStart=\"routerStart\",T.ViewRenderEnd=\"viewRenderEnd\",(S=ea||(ea={})).More=\"more\",S.Share=\"share\",(b=es||(es={})).Center=\"center\",b.Event=\"event\",b.General=\"general\",b.GoodsDetail=\"goodsDetail\",b.Topic=\"topic\",b.XiuxiuInvite=\"xiuxiuInvite\",(k=eu||(eu={})).Image=\"image\",k.Link=\"link\",k.MiniProgram=\"miniProgram\",k.Text=\"text\",(C=ec||(ec={})).Image=\"image\",C.Link=\"link\",(P=el||(el={})).Image=\"image\",P.Link=\"link\",P.Text=\"text\",(A=ed||(ed={})).Emoji=\"emoji\",A.Image=\"image\",A.Link=\"link\",A.MiniProgram=\"miniProgram\",A.Text=\"text\",(R=ep||(ep={})).Image=\"image\",R.Link=\"link\",R.Text=\"text\",(I=ef||(ef={})).Image=\"image\",I.Link=\"link\",I.Text=\"text\",(O=ev||(ev={})).Goods=\"goods\",O.Universal=\"universal\",(N=eh||(eh={})).PageComplete=\"page_complete\",N.PageLoad=\"page_load\",(L=eg||(eg={})).Pause=\"pause\",L.Resume=\"resume\"},92014:function(e,r,i){\"use strict\"", "position": 405827}, "J": {"value": "i(1799)", "position": 416040}, "M": {"value": "i(3511)", "position": 429780}, "D": {"value": "s.YF.isAndroid&&(0,s.S8)(\"5.20\")||!0", "position": 432635}, "V": {"value": "Symbol(\"http\"),install=function(e,r,i){var a=i", "position": 331677}, "H": {"value": "window.XHS_CALLBACKS||(window.XHS_CALLBACKS={})", "position": 378739}, "W": {"value": "new WeakMap,G=function(){function XHSEventsCB(){(0,F._)(this,XHSEventsCB),(0,H._)(this,W,{writable:!0,value:{}})}return(0,U._)(XHSEventsCB,[{key:\"get\",value:function get(e){return(0,V._)(this,W)[e]||[]}},{key:\"set\",value:function set(e,r){(0,V._)(this,W)[e]=r}}]),XHSEventsCB}()", "position": 414936}, "I": {"value": "(O.major>6||6===O.major&&O.minor>=7)&&m.isFullscreen||_&&m.isNavi<PERSON>idden||C&&m.isFullscreen}return(0,s._)((0,a._)({},d),{isIOS:_,isAndroid:y,isHarmony:w,isHarmonyArk:E,isXHS:k,isFullscreen:I,isWeixin:\"micromessenger\"===v,isAlipay:\"alipay\"===v,isWeibo:\"weibo\"===v,isQQ:\"qq\"===v,isQQBrowser:\"mqqbrowser\"===v,isMiniprogram:h===l.weixin||m.isMiniprogram,isBaiduMiniprogram:h===l.baidu,isQQMiniprogram:h===l.qq,isAlipayMiniprogram:h===l.alipay,isToutiaoMiniprogram:h===l.touti<PERSON>,isIphone14Pro:iphone14ProCheck(f),isIphoneX:g.isIphoneX,iphoneXType:g.iphoneXType,isTop:C,isUniik:\"uniik\"===v,isSpark:\"spark\"===v,isXhsMerchant:\"merchant\"===v,isSnowPeak:\"snowpeak\"===v,isInternation:\"internation\"===v,isCatalog:\"catalog\"===v,isOdyssey:\"odyssey\"===v,isPC:R,isMobile:P,buildNumber:A})}function getNetTypeFromUA(e){var r=\"Unrecognized\",i=[\"2g\",\"3g\",\"4g\",\"wifi\",\"cellnetwork\"]", "position": 369624}, "X": {"value": "i(27256),Y=i(41622),z=i(377)", "position": 417276}, "V2": {"value": "function(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}", "position": 307335}, "G": {"value": "0,eJ=0,addEventSeqIdInSession=function(e,r){if(!!e&&!!r){(null==e?void 0:null===(i=e.event)||void 0===i?void 0:i.value)&&(e.event.value.seqId=Date.now()),(null==e?void 0:null===(a=e.app)||void 0===a?void 0:a.value)&&(eG+=1,e.app.value.eventSeqIdInSession=eG)", "position": 300360}, "Y": {"value": "function(e){function BridgeError(e){for(var r,i=arguments.length,a=Array(i>1?i-1:0),s=1", "position": 377727}, "RT": {"value": "u.duration),(\"popstate\"===i||sessionStorage.getItem(\"__SPA_LCP__\"+window.location.pathname))&&(s.type=\"bfcache\"),r(s),sessionStorage.setItem(\"__SPA_LCP__\"+window.location.pathname,\"reported\")}})", "position": 88832}, "LCP": {"value": "function(e){PerformanceObserver.supportedEntryTypes.includes(\"largest-contentful-paint\")?(0,eK.NO)(e):whenActivated(function(){var r=window.__LDM__OBSERVER,i={name:\"LCP\",value:-1,attribution:{}},a=runOnce(function(){try{if(r&&r.disconnect(),r){var a,s=r.takeEntry()", "position": 81488}, "FMP": {"value": "function(e,r){if(r.custom){var i={name:\"CustomFMP\",type:\"unknown\",value:-1,element:\"\"},a=runOnce(function(r){if(\"object\"===(0,S._)(window.__CUSTOM_FMP_METRICS__)){var a=window.__CUSTOM_FMP_METRICS__", "position": 83229}, "TTI": {"value": "function(e){var r,i", "position": 91702}, "FPS": {"value": "function(e,r){whenActivated(function(){r&&r.observe({reportFrameThreshold:60,lagFrameThreshold:125})}),onActivated(function(){r&&\"observe\"!==r.status&&r.observe({reportFrameThreshold:60,lagFrameThreshold:125})}),onHidden(function(){if(r){r.disconnect()", "position": 95855}, "INP": {"value": "function(e){var r=[],i=null", "position": 97013}, "CLS": {"value": "function(e){var r=[],i=null", "position": 109794}, "NRE": {"value": "function(){try{if(!window.PerformanceObserver)return", "position": 120724}, "PV": {"value": "function(r){try{if(r){var i,a,s=(null===(i=window.screen)||void 0===i?void 0:i.width)||0,u=(null===(a=window.screen)||void 0===a?void 0:a.height)||0", "position": 124199}, "UA": {"value": "getUA,r.getDefaultDeviceInfo=getDefaultDeviceInfo},34493:function(e,r,i){\"use strict\"", "position": 537136}, "URL": {"value": "f(),a.instance=r,a.fork=function(){var e=s().create()", "position": 149680}, "OS": {"value": "1]=\"iOS\",a[a.Android=2]=\"Android\",a[a.MacOs=3]=\"MacOs\",a[a.Linux=4]=\"Linux\",a[a.other=5]=\"other\"", "position": 180995}, "IOS": {"value": "(0,A._)(function(e){var r", "position": 449746}, "CB": {"value": "window.XHSEventsCB||new G", "position": 416007}, "SEQ": {"value": "0),Int.SEQ++}}]),Int}()", "position": 549487}}, "variable_mappings": {}, "algorithm_analysis": {"parameters": [], "local_variables": {"s": "e.platform", "c": "r.headers[\"X-Sign\"]||\"\""}, "object_structure": {"s0": "getPlatformCode(s)", "s1": "\"\"", "x0": "f", "x1": "C", "x2": "s||\"PC\"", "x3": "\"xhs-pc-web\"", "x4": "\"4.68.0\"", "x5": "l.Z.get(\"a1\")", "x6": "\"\"", "x7": "\"\"", "x8": "p", "x9": "O(\"\".concat(\"\").concat(\"\").concat(p))", "x10": "d", "x11": "\"normal\""}, "key_operations": [{"operation": "localStorage.getItem(\"b1\")", "type": "storage_access", "position": 197}, {"operation": "localStorage.getItem(\"b1b1\")", "type": "storage_access", "position": 226}, {"operation": "JSON.stringify(v)", "type": "serialization", "position": 758}, {"operation": "JSON.stringify(v)", "type": "serialization", "position": 825}, {"operation": "b64Encode(encodeUtf8(JSON.stringify(v)", "type": "serialization", "position": 737}, {"operation": "b64Encode(encodeUtf8(JSON.stringify(v)", "type": "serialization", "position": 804}, {"operation": "encodeUtf8(JSON.stringify(v)", "type": "serialization", "position": 747}, {"operation": "encodeUtf8(JSON.stringify(v)", "type": "serialization", "position": 814}, {"operation": "headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}", "type": "serialization", "position": 715}, {"operation": ".get(\"a1\")", "type": "data_access", "position": 346}, {"operation": "O(\"\".concat(\"\")", "type": "hash_or_encrypt", "position": 377}, {"operation": "O(\"\".concat(\"\")", "type": "hash_or_encrypt", "position": 675}], "return_logic": ["new RegExp(e)}).some(function(e){return e.test(u)}),!utils_shouldSign(u))return r", "new RegExp(e)}).some(function(e){return e.test(u)})"]}}