function xsCommon(e, r) {
    var i, a;
    try {
        var s = e.platform,
            u = r.url;
        
        // 检查URL是否匹配某些模式
        if (S.map(function(e) {
            return new RegExp(e);
        }).some(function(e) {
            return e.test(u);
        }), !utils_shouldSign(u)) {
            return r;
        }
        
        // 收集基础数据
        var c = r.headers["X-Sign"] || "",
            d = getSigCount(c),
            p = localStorage.getItem("b1"),
            f = localStorage.getItem("b1b1") || "1";
        
        // 构建核心对象
        var v = {
            s0: getPlatformCode(s),     // 平台代码
            s1: "",                     // 空字符串
            x0: f,                      // b1b1值
            x1: C,                      // 常量C
            x2: s || "PC",              // 平台信息
            x3: "xhs-pc-web",           // 应用标识
            x4: "4.68.0",               // 版本号
            x5: l.<PERSON>.get("a1"),          // a1值
            x6: "",                     // 空字符串
            x7: "",                     // 空字符串
            x8: p,                      // b1值
            x9: O("".concat("").concat("").concat(p)),  // b1的哈希值
            x10: d,                     // 签名计数
            x11: "normal"               // 固定值
        };
        
        // 检查是否需要使用指纹
        var h = k.map(function(e) {
            return new RegExp(e);
        }).some(function(e) {
            return e.test(u);
        });
        
        // 根据指纹情况设置请求头
        if ((null === (i = window.xhsFingerprintV3) || void 0 === i ? void 0 : i.getCurMiniUa) && h) {
            // 异步获取指纹并设置
            null === (a = window.xhsFingerprintV3) || void 0 === a || a.getCurMiniUa(function(e) {
                v.x8 = e;  // 用指纹替换b1值
                v.x9 = O("".concat("").concat("").concat(e));  // 用指纹的哈希替换
                r.headers["X-S-Common"] = b64Encode(encodeUtf8(JSON.stringify(v)));
            });
        } else {
            // 直接设置请求头
            r.headers["X-S-Common"] = b64Encode(encodeUtf8(JSON.stringify(v)));
        }
        
    } catch (e) {
        // 忽略所有错误
    }
    
    return r;
}