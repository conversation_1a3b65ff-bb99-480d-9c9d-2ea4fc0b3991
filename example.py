#!/usr/bin/env python3
"""
JavaScript抓取脚本使用示例
"""

from js_scraper import JavaScriptScraper


def example_usage():
    """示例用法"""
    
    # 示例1: 基本用法
    print("=== 示例1: 基本用法 ===")
    url1 = "https://www.baidu.com"
    scraper1 = JavaScriptScraper(url1, "example_output1")
    scraper1.scrape()
    
    print("\n" + "="*50 + "\n")
    
    # 示例2: 自定义配置
    print("=== 示例2: 自定义配置 ===")
    url2 = "https://www.github.com"
    scraper2 = JavaScriptScraper(
        base_url=url2,
        output_dir="example_output2", 
        timeout=15
    )
    scraper2.scrape()


if __name__ == "__main__":
    example_usage()
